<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>元素验证 - <PERSON>'s Meeting Note</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .validation-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .validation-section h3 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .element-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .element-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .element-item.found {
            border-left-color: #28a745;
        }
        .element-item.missing {
            border-left-color: #dc3545;
        }
        .element-status {
            margin-right: 10px;
            font-size: 16px;
        }
        .element-info {
            flex: 1;
        }
        .element-id {
            font-weight: bold;
            color: #2c3e50;
        }
        .element-desc {
            font-size: 12px;
            color: #6c757d;
        }
        .summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        .summary.success {
            background: #d4edda;
            color: #155724;
        }
        .summary.warning {
            background: #fff3cd;
            color: #856404;
        }
        .summary.error {
            background: #f8d7da;
            color: #721c24;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Jason's Meeting Note - 元素验证</h1>
            <p>检查主应用中的所有必需HTML元素</p>
        </div>

        <div id="summary" class="summary">
            <h3>正在检查元素...</h3>
        </div>

        <div class="validation-section">
            <h3>🎛️ AI设置相关元素</h3>
            <div id="aiElements" class="element-list">
                <!-- AI元素将在这里动态生成 -->
            </div>
        </div>

        <div class="validation-section">
            <h3>🎤 语音识别相关元素</h3>
            <div id="voiceElements" class="element-list">
                <!-- 语音元素将在这里动态生成 -->
            </div>
        </div>

        <div class="validation-section">
            <h3>📝 界面控制相关元素</h3>
            <div id="uiElements" class="element-list">
                <!-- UI元素将在这里动态生成 -->
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="window.location.reload()">🔄 重新检查</button>
            <a href="meeting-notes.html" class="btn btn-success">📱 打开主应用</a>
            <a href="debug-test.html" class="btn">🛠️ 调试工具</a>
        </div>
    </div>

    <script>
        // 元素定义
        const elementGroups = {
            ai: {
                title: 'AI设置相关元素',
                elements: {
                    'toggleAISettings': 'AI设置切换按钮',
                    'aiSettingsPanel': 'AI设置面板',
                    'aiProvider': 'AI服务商选择',
                    'apiKey': 'API Key输入框',
                    'apiBaseUrl': 'API Base URL输入框',
                    'modelName': '模型名称选择',
                    'temperature': '温度滑块',
                    'maxTokens': '最大Token数输入框',
                    'testConnection': '测试连接按钮',
                    'saveAIConfig': '保存AI配置按钮'
                }
            },
            voice: {
                title: '语音识别相关元素',
                elements: {
                    'startRecording': '开始录音按钮',
                    'stopRecording': '停止录音按钮',
                    'pauseRecording': '暂停录音按钮',
                    'recordingStatus': '录音状态指示器',
                    'transcriptionText': '语音转录显示区域',
                    'clearTranscription': '清空转录按钮'
                }
            },
            ui: {
                title: '界面控制相关元素',
                elements: {
                    'meetingTitle': '会议主题输入框',
                    'meetingTime': '会议时间输入框',
                    'participants': '参会人员输入框',
                    'manualNotes': '手动笔记文本框',
                    'generateSummary': '生成纪要按钮',
                    'meetingSummary': '会议纪要显示区域',
                    'saveMeeting': '保存会议按钮',
                    'exportMeeting': '导出会议按钮'
                }
            }
        };

        // 检查元素是否存在
        function checkElement(elementId) {
            // 尝试在当前页面查找
            let element = document.getElementById(elementId);
            
            if (!element) {
                // 如果当前页面没有，尝试在iframe中查找主应用
                const iframe = document.querySelector('iframe');
                if (iframe && iframe.contentDocument) {
                    element = iframe.contentDocument.getElementById(elementId);
                }
            }
            
            return element !== null;
        }

        // 创建元素项
        function createElementItem(elementId, description, found) {
            const item = document.createElement('div');
            item.className = `element-item ${found ? 'found' : 'missing'}`;
            
            item.innerHTML = `
                <span class="element-status">${found ? '✅' : '❌'}</span>
                <div class="element-info">
                    <div class="element-id">${elementId}</div>
                    <div class="element-desc">${description}</div>
                </div>
            `;
            
            return item;
        }

        // 运行验证
        function runValidation() {
            let totalElements = 0;
            let foundElements = 0;
            
            // 检查每个组的元素
            Object.keys(elementGroups).forEach(groupKey => {
                const group = elementGroups[groupKey];
                const container = document.getElementById(groupKey + 'Elements');
                container.innerHTML = '';
                
                Object.keys(group.elements).forEach(elementId => {
                    totalElements++;
                    const description = group.elements[elementId];
                    const found = checkElement(elementId);
                    
                    if (found) foundElements++;
                    
                    const item = createElementItem(elementId, description, found);
                    container.appendChild(item);
                });
            });
            
            // 更新总结
            updateSummary(totalElements, foundElements);
        }

        // 更新总结信息
        function updateSummary(total, found) {
            const summary = document.getElementById('summary');
            const percentage = Math.round((found / total) * 100);
            
            let className = 'summary ';
            let message = '';
            
            if (percentage === 100) {
                className += 'success';
                message = `🎉 完美！所有 ${total} 个元素都已找到 (${percentage}%)`;
            } else if (percentage >= 80) {
                className += 'warning';
                message = `⚠️ 大部分元素已找到：${found}/${total} (${percentage}%)`;
            } else {
                className += 'error';
                message = `❌ 发现问题：只找到 ${found}/${total} 个元素 (${percentage}%)`;
            }
            
            summary.className = className;
            summary.innerHTML = `
                <h3>${message}</h3>
                <p>缺失元素: ${total - found} 个</p>
                ${percentage < 100 ? '<p>建议：检查主应用页面 (meeting-notes.html) 是否正确加载</p>' : ''}
            `;
        }

        // 页面加载完成后运行验证
        window.addEventListener('load', () => {
            // 延迟一点时间确保所有资源加载完成
            setTimeout(() => {
                runValidation();
            }, 500);
        });

        // 定期重新检查（每5秒）
        setInterval(() => {
            runValidation();
        }, 5000);
    </script>
</body>
</html>
