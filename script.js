// <PERSON>'s Meeting Note - 主要JavaScript功能

// AI配置管理类
class AIConfigManager {
    constructor() {
        this.apiConfigs = {
            deepseek: {
                name: 'DeepSeek',
                baseUrl: 'https://api.deepseek.com/v1',
                models: ['deepseek-chat', 'deepseek-coder'],
                headers: { 'Authorization': 'Bearer {apiKey}' }
            },
            minimax: {
                name: 'MiniMax',
                baseUrl: 'https://api.minimax.chat/v1',
                models: ['abab6.5s-chat', 'abab6.5-chat', 'abab5.5s-chat'],
                headers: { 'Authorization': 'Bearer {api<PERSON><PERSON>}' }
            },
            doubao: {
                name: '豆包(字节跳动)',
                baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
                models: ['ep-20241217-*****', 'ep-20241217-*****'],
                headers: { 'Authorization': 'Bearer {api<PERSON><PERSON>}' }
            },
            gemini: {
                name: 'Google Gemini',
                baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
                models: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro'],
                headers: { 'x-goog-api-key': '{apiKey}' }
            },
            openai: {
                name: 'OpenAI GPT',
                baseUrl: 'https://api.openai.com/v1',
                models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
                headers: { 'Authorization': 'Bearer {apiKey}' }
            },
            custom: {
                name: '自定义API',
                baseUrl: '',
                models: [],
                headers: { 'Authorization': 'Bearer {apiKey}' }
            }
        };

        this.currentConfig = this.loadConfig();
    }

    // 加载保存的配置
    loadConfig() {
        const saved = localStorage.getItem('aiConfig');
        return saved ? JSON.parse(saved) : null;
    }

    // 保存配置
    saveConfig(config) {
        this.currentConfig = config;
        localStorage.setItem('aiConfig', JSON.stringify(config));
    }

    // 获取API配置
    getApiConfig(provider) {
        return this.apiConfigs[provider] || null;
    }

    // 获取当前配置
    getCurrentConfig() {
        return this.currentConfig;
    }

    // 清空配置
    clearConfig() {
        this.currentConfig = null;
        localStorage.removeItem('aiConfig');
    }
}

// AI服务调用类
class AIService {
    constructor(configManager) {
        this.configManager = configManager;
        this.requestTimeout = 30000; // 30秒超时
    }

    // 测试API连接
    async testConnection(config) {
        try {
            const testPrompt = "Hello, this is a connection test.";
            const response = await this.makeRequest(config, testPrompt, { maxTokens: 10 });
            return { success: true, message: '连接成功' };
        } catch (error) {
            return { success: false, message: error.message };
        }
    }

    // 生成会议纪要
    async generateSummary(config, content, mode, customPrompt = '') {
        const prompts = {
            'ai-concise': '请将以下会议内容总结为简洁的纪要，包含主要讨论点和决策：',
            'ai-detailed': '请将以下会议内容整理为详细的会议纪要，包含完整的讨论过程、决策和行动项：',
            'ai-action': '请从以下会议内容中提取所有行动项、决策和待办事项，并按优先级整理：',
            'ai-custom': customPrompt || '请总结以下会议内容：'
        };

        const prompt = prompts[mode] + '\n\n' + content;
        return await this.makeRequest(config, prompt);
    }

    // 发送API请求
    async makeRequest(config, prompt, options = {}) {
        const { provider, apiKey, baseUrl, model, temperature = 0.7, maxTokens = 2000 } = config;

        if (!apiKey) {
            throw new Error('API Key未配置');
        }

        const apiConfig = this.configManager.getApiConfig(provider);
        if (!apiConfig) {
            throw new Error('不支持的API服务商');
        }

        const url = (baseUrl || apiConfig.baseUrl);
        const headers = { ...apiConfig.headers };

        // 替换API Key
        Object.keys(headers).forEach(key => {
            headers[key] = headers[key].replace('{apiKey}', apiKey);
        });

        const requestBody = this.buildRequestBody(provider, prompt, model, {
            temperature: options.temperature || temperature,
            maxTokens: options.maxTokens || maxTokens
        });

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

        try {
            const response = await fetch(this.getEndpoint(url, provider), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...headers
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API请求失败: ${response.status} - ${errorData.error?.message || response.statusText}`);
            }

            const data = await response.json();
            return this.extractResponse(provider, data);

        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请检查网络连接');
            }
            throw error;
        }
    }

    // 构建请求体
    buildRequestBody(provider, prompt, model, options) {
        const baseBody = {
            model: model,
            temperature: options.temperature,
            max_tokens: options.maxTokens
        };

        switch (provider) {
            case 'openai':
            case 'deepseek':
            case 'minimax':
            case 'doubao':
                return {
                    ...baseBody,
                    messages: [{ role: 'user', content: prompt }]
                };
            case 'gemini':
                return {
                    contents: [{ parts: [{ text: prompt }] }],
                    generationConfig: {
                        temperature: options.temperature,
                        maxOutputTokens: options.maxTokens
                    }
                };
            default:
                return {
                    ...baseBody,
                    messages: [{ role: 'user', content: prompt }]
                };
        }
    }

    // 获取API端点
    getEndpoint(baseUrl, provider) {
        switch (provider) {
            case 'gemini':
                return `${baseUrl}/models/gemini-pro:generateContent`;
            default:
                return `${baseUrl}/chat/completions`;
        }
    }

    // 提取响应内容
    extractResponse(provider, data) {
        switch (provider) {
            case 'gemini':
                return data.candidates?.[0]?.content?.parts?.[0]?.text || '无响应内容';
            default:
                return data.choices?.[0]?.message?.content || '无响应内容';
        }
    }
}

class MeetingNoteApp {
    constructor() {
        this.recognition = null;
        this.isRecording = false;
        this.isPaused = false;
        this.transcriptionText = '';
        this.currentMeeting = null;

        // 初始化AI功能
        this.aiConfigManager = new AIConfigManager();
        this.aiService = new AIService(this.aiConfigManager);

        this.initializeApp();
        this.setupEventListeners();
        this.checkBrowserSupport();
    }

    // 初始化应用
    initializeApp() {
        console.log('Jason\'s Meeting Note 应用初始化中...');
        this.loadSavedMeetings();
        this.setDefaultDateTime();
        this.initializeAISettings();
    }

    // 初始化AI设置
    initializeAISettings() {
        const savedConfig = this.aiConfigManager.getCurrentConfig();
        if (savedConfig) {
            this.loadAIConfig(savedConfig);
        }
        this.updateConnectionStatus('unknown', '未测试');
    }

    // 检查浏览器支持
    checkBrowserSupport() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            this.showModal('抱歉，您的浏览器不支持语音识别功能。请使用Chrome、Edge或Safari浏览器。');
            document.getElementById('startRecording').disabled = true;
            return false;
        }
        return true;
    }

    // 设置默认日期时间
    setDefaultDateTime() {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
            .toISOString().slice(0, 16);
        document.getElementById('meetingTime').value = localDateTime;
    }

    // 设置事件监听器
    setupEventListeners() {
        // 语音控制按钮
        document.getElementById('startRecording').addEventListener('click', () => this.startRecording());
        document.getElementById('stopRecording').addEventListener('click', () => this.stopRecording());
        document.getElementById('pauseRecording').addEventListener('click', () => this.pauseRecording());

        // 清空按钮
        document.getElementById('clearTranscription').addEventListener('click', () => this.clearTranscription());
        document.getElementById('clearManualNotes').addEventListener('click', () => this.clearManualNotes());

        // 时间戳按钮
        document.getElementById('addTimestamp').addEventListener('click', () => this.addTimestamp());

        // 生成纪要按钮
        document.getElementById('generateSummary').addEventListener('click', () => this.generateSummary());

        // 操作按钮
        document.getElementById('saveMeeting').addEventListener('click', () => this.saveMeeting());
        document.getElementById('exportMeeting').addEventListener('click', () => this.exportMeeting());
        document.getElementById('newMeeting').addEventListener('click', () => this.newMeeting());
        document.getElementById('loadMeeting').addEventListener('click', () => this.toggleHistorySection());

        // AI设置相关事件
        document.getElementById('toggleAiSettings').addEventListener('click', () => this.toggleAISettings());
        document.getElementById('apiProvider').addEventListener('change', (e) => this.onApiProviderChange(e.target.value));
        document.getElementById('toggleApiKey').addEventListener('click', () => this.toggleApiKeyVisibility());
        document.getElementById('toggleAdvanced').addEventListener('click', () => this.toggleAdvancedParams());
        document.getElementById('testConnection').addEventListener('click', () => this.testAPIConnection());
        document.getElementById('saveAiConfig').addEventListener('click', () => this.saveAIConfig());
        document.getElementById('clearAiConfig').addEventListener('click', () => this.clearAIConfig());

        // 纪要生成模式切换
        document.getElementById('summaryMode').addEventListener('change', (e) => this.onSummaryModeChange(e.target.value));

        // 高级参数滑块
        document.getElementById('temperature').addEventListener('input', (e) => {
            document.getElementById('temperatureValue').textContent = e.target.value;
        });

        // 模态框关闭
        document.querySelector('.close').addEventListener('click', () => this.closeModal());
        window.addEventListener('click', (event) => {
            const modal = document.getElementById('statusModal');
            if (event.target === modal) {
                this.closeModal();
            }
        });
    }

    // 开始录音
    startRecording() {
        if (!this.checkBrowserSupport()) return;

        try {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            
            // 配置语音识别
            this.recognition.continuous = true;
            this.recognition.interimResults = true;
            this.recognition.lang = 'zh-CN'; // 默认中文，可以根据需要调整
            
            // 语音识别事件处理
            this.recognition.onstart = () => {
                this.isRecording = true;
                this.isPaused = false;
                this.updateRecordingUI();
                console.log('语音识别已开始');
            };
            
            this.recognition.onresult = (event) => {
                let interimTranscript = '';
                let finalTranscript = '';
                
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    if (event.results[i].isFinal) {
                        finalTranscript += transcript + ' ';
                    } else {
                        interimTranscript += transcript;
                    }
                }
                
                this.updateTranscriptionDisplay(finalTranscript, interimTranscript);
            };
            
            this.recognition.onerror = (event) => {
                console.error('语音识别错误:', event.error);
                this.showModal(`语音识别出现错误: ${event.error}`);
                this.stopRecording();
            };
            
            this.recognition.onend = () => {
                if (this.isRecording && !this.isPaused) {
                    // 如果还在录音状态但识别结束了，重新开始
                    setTimeout(() => {
                        if (this.isRecording) {
                            this.recognition.start();
                        }
                    }, 100);
                }
            };
            
            this.recognition.start();
            
        } catch (error) {
            console.error('启动语音识别失败:', error);
            this.showModal('启动语音识别失败，请检查麦克风权限。');
        }
    }

    // 停止录音
    stopRecording() {
        if (this.recognition) {
            this.isRecording = false;
            this.isPaused = false;
            this.recognition.stop();
            this.updateRecordingUI();
            console.log('语音识别已停止');
        }
    }

    // 暂停录音
    pauseRecording() {
        if (this.recognition && this.isRecording) {
            this.isPaused = !this.isPaused;
            
            if (this.isPaused) {
                this.recognition.stop();
            } else {
                this.recognition.start();
            }
            
            this.updateRecordingUI();
        }
    }

    // 更新录音UI状态
    updateRecordingUI() {
        const startBtn = document.getElementById('startRecording');
        const stopBtn = document.getElementById('stopRecording');
        const pauseBtn = document.getElementById('pauseRecording');
        const status = document.getElementById('recordingStatus');
        
        if (this.isRecording) {
            if (this.isPaused) {
                startBtn.disabled = false;
                stopBtn.disabled = false;
                pauseBtn.disabled = false;
                pauseBtn.innerHTML = '<i class="fas fa-play"></i> 继续';
                status.textContent = '已暂停';
                status.className = 'status-indicator paused';
            } else {
                startBtn.disabled = true;
                stopBtn.disabled = false;
                pauseBtn.disabled = false;
                pauseBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
                status.textContent = '正在录音';
                status.className = 'status-indicator recording';
            }
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            pauseBtn.disabled = true;
            pauseBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
            status.textContent = '准备就绪';
            status.className = 'status-indicator';
        }
    }

    // 更新转录显示
    updateTranscriptionDisplay(finalText, interimText) {
        const transcriptionDiv = document.getElementById('transcriptionText');
        
        if (finalText) {
            this.transcriptionText += finalText;
        }
        
        const displayText = this.transcriptionText + (interimText ? `<span style="color: #999; font-style: italic;">${interimText}</span>` : '');
        
        if (displayText.trim()) {
            transcriptionDiv.innerHTML = `<div style="white-space: pre-wrap;">${displayText}</div>`;
        } else {
            transcriptionDiv.innerHTML = '<p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>';
        }
        
        // 自动滚动到底部
        transcriptionDiv.scrollTop = transcriptionDiv.scrollHeight;
    }

    // 清空转录内容
    clearTranscription() {
        this.transcriptionText = '';
        document.getElementById('transcriptionText').innerHTML = '<p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>';
    }

    // 清空手动笔记
    clearManualNotes() {
        document.getElementById('manualNotes').value = '';
    }

    // 添加时间戳
    addTimestamp() {
        const textarea = document.getElementById('manualNotes');
        const now = new Date();
        const timestamp = `[${now.toLocaleTimeString()}] `;

        const cursorPos = textarea.selectionStart;
        const textBefore = textarea.value.substring(0, cursorPos);
        const textAfter = textarea.value.substring(cursorPos);

        textarea.value = textBefore + timestamp + textAfter;
        textarea.focus();
        textarea.setSelectionRange(cursorPos + timestamp.length, cursorPos + timestamp.length);
    }

    // AI设置相关方法

    // 切换AI设置面板
    toggleAISettings() {
        const panel = document.getElementById('aiSettingsPanel');
        const isVisible = panel.style.display !== 'none';
        panel.style.display = isVisible ? 'none' : 'block';

        const button = document.getElementById('toggleAiSettings');
        button.innerHTML = isVisible ?
            '<i class="fas fa-cog"></i> 配置AI' :
            '<i class="fas fa-times"></i> 关闭配置';
    }

    // API服务商变更
    onApiProviderChange(provider) {
        const configArea = document.getElementById('apiConfigArea');
        const modelSelect = document.getElementById('modelName');
        const baseUrlInput = document.getElementById('apiBaseUrl');

        if (provider) {
            configArea.style.display = 'block';

            const apiConfig = this.aiConfigManager.getApiConfig(provider);
            if (apiConfig) {
                // 更新模型选项
                modelSelect.innerHTML = '<option value="">请选择模型</option>';
                apiConfig.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    modelSelect.appendChild(option);
                });

                // 设置默认Base URL
                baseUrlInput.value = apiConfig.baseUrl;
            }
        } else {
            configArea.style.display = 'none';
        }

        this.updateConnectionStatus('unknown', '未测试');
    }

    // 切换API Key可见性
    toggleApiKeyVisibility() {
        const input = document.getElementById('apiKey');
        const button = document.getElementById('toggleApiKey');

        if (input.type === 'password') {
            input.type = 'text';
            button.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            input.type = 'password';
            button.innerHTML = '<i class="fas fa-eye"></i>';
        }
    }

    // 切换高级参数
    toggleAdvancedParams() {
        const params = document.getElementById('advancedParams');
        const button = document.getElementById('toggleAdvanced');
        const isVisible = params.style.display !== 'none';

        params.style.display = isVisible ? 'none' : 'block';
        button.innerHTML = isVisible ?
            '<i class="fas fa-sliders-h"></i> 高级参数' :
            '<i class="fas fa-sliders-h"></i> 隐藏参数';
    }

    // 纪要生成模式变更
    onSummaryModeChange(mode) {
        const customArea = document.getElementById('customPromptArea');
        customArea.style.display = mode === 'ai-custom' ? 'block' : 'none';
    }

    // 测试API连接
    async testAPIConnection() {
        const config = this.getAIConfigFromForm();
        if (!config.apiKey) {
            this.showModal('请先输入API Key');
            return;
        }

        this.updateConnectionStatus('testing', '测试中...');

        try {
            const result = await this.aiService.testConnection(config);
            if (result.success) {
                this.updateConnectionStatus('connected', '连接成功');
                this.showModal('API连接测试成功！');
            } else {
                this.updateConnectionStatus('error', '连接失败');
                this.showModal(`连接测试失败: ${result.message}`);
            }
        } catch (error) {
            this.updateConnectionStatus('error', '连接失败');
            this.showModal(`连接测试失败: ${error.message}`);
        }
    }

    // 保存AI配置
    saveAIConfig() {
        const config = this.getAIConfigFromForm();
        if (!config.provider || !config.apiKey) {
            this.showModal('请完整填写API服务商和API Key');
            return;
        }

        this.aiConfigManager.saveConfig(config);
        this.showModal('AI配置已保存成功！');
    }

    // 清空AI配置
    clearAIConfig() {
        if (confirm('确定要清空AI配置吗？')) {
            this.aiConfigManager.clearConfig();
            this.clearAIConfigForm();
            this.updateConnectionStatus('unknown', '未测试');
            this.showModal('AI配置已清空');
        }
    }

    // 从表单获取AI配置
    getAIConfigFromForm() {
        return {
            provider: document.getElementById('apiProvider').value,
            apiKey: document.getElementById('apiKey').value,
            baseUrl: document.getElementById('apiBaseUrl').value,
            model: document.getElementById('modelName').value,
            temperature: parseFloat(document.getElementById('temperature').value),
            maxTokens: parseInt(document.getElementById('maxTokens').value)
        };
    }

    // 加载AI配置到表单
    loadAIConfig(config) {
        document.getElementById('apiProvider').value = config.provider || '';
        document.getElementById('apiKey').value = config.apiKey || '';
        document.getElementById('apiBaseUrl').value = config.baseUrl || '';
        document.getElementById('modelName').value = config.model || '';
        document.getElementById('temperature').value = config.temperature || 0.7;
        document.getElementById('temperatureValue').textContent = config.temperature || 0.7;
        document.getElementById('maxTokens').value = config.maxTokens || 2000;

        if (config.provider) {
            this.onApiProviderChange(config.provider);
        }
    }

    // 清空AI配置表单
    clearAIConfigForm() {
        document.getElementById('apiProvider').value = '';
        document.getElementById('apiKey').value = '';
        document.getElementById('apiBaseUrl').value = '';
        document.getElementById('modelName').innerHTML = '<option value="">请先选择API服务商</option>';
        document.getElementById('temperature').value = 0.7;
        document.getElementById('temperatureValue').textContent = '0.7';
        document.getElementById('maxTokens').value = 2000;
        document.getElementById('apiConfigArea').style.display = 'none';
    }

    // 更新连接状态
    updateConnectionStatus(status, text) {
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.querySelector('.status-text');

        statusDot.className = `status-dot status-${status}`;
        statusText.textContent = text;
    }

    // 生成会议纪要
    async generateSummary() {
        const meetingTitle = document.getElementById('meetingTitle').value || '未命名会议';
        const meetingTime = document.getElementById('meetingTime').value;
        const participants = document.getElementById('participants').value;
        const transcription = this.transcriptionText;
        const manualNotes = document.getElementById('manualNotes').value;
        const summaryMode = document.getElementById('summaryMode').value;

        if (!transcription.trim() && !manualNotes.trim()) {
            this.showModal('请先录制语音或输入手动笔记后再生成纪要。');
            return;
        }

        // 基础模式直接生成
        if (summaryMode === 'basic') {
            const summary = this.createMeetingSummary({
                title: meetingTitle,
                time: meetingTime,
                participants: participants,
                transcription: transcription,
                manualNotes: manualNotes
            });
            document.getElementById('meetingSummary').innerHTML = summary;
            return;
        }

        // AI模式需要检查配置
        const aiConfig = this.aiConfigManager.getCurrentConfig();
        if (!aiConfig || !aiConfig.apiKey) {
            this.showModal('请先配置AI服务后再使用AI生成功能。');
            return;
        }

        // 显示进度指示器
        this.showGenerationProgress(true);

        try {
            // 准备内容
            const content = this.prepareContentForAI(meetingTitle, meetingTime, participants, transcription, manualNotes);

            // 获取自定义提示词
            const customPrompt = summaryMode === 'ai-custom' ?
                document.getElementById('customPrompt').value : '';

            // 调用AI生成
            const aiSummary = await this.aiService.generateSummary(aiConfig, content, summaryMode, customPrompt);

            // 生成最终纪要
            const summary = this.createAIMeetingSummary({
                title: meetingTitle,
                time: meetingTime,
                participants: participants,
                transcription: transcription,
                manualNotes: manualNotes,
                aiSummary: aiSummary,
                mode: summaryMode
            });

            document.getElementById('meetingSummary').innerHTML = summary;

        } catch (error) {
            console.error('AI生成失败:', error);
            this.showModal(`AI生成失败: ${error.message}`);

            // 失败时回退到基础模式
            const summary = this.createMeetingSummary({
                title: meetingTitle,
                time: meetingTime,
                participants: participants,
                transcription: transcription,
                manualNotes: manualNotes
            });
            document.getElementById('meetingSummary').innerHTML = summary;

        } finally {
            this.showGenerationProgress(false);
        }
    }

    // 准备AI处理的内容
    prepareContentForAI(title, time, participants, transcription, manualNotes) {
        let content = `会议主题: ${title}\n`;
        if (time) {
            const date = new Date(time);
            content += `会议时间: ${date.toLocaleString('zh-CN')}\n`;
        }
        if (participants) {
            content += `参会人员: ${participants}\n`;
        }
        content += '\n';

        if (transcription.trim()) {
            content += '语音转录内容:\n' + transcription + '\n\n';
        }

        if (manualNotes.trim()) {
            content += '手动记录内容:\n' + manualNotes + '\n';
        }

        return content;
    }

    // 显示/隐藏生成进度
    showGenerationProgress(show) {
        const progress = document.getElementById('generationProgress');
        progress.style.display = show ? 'block' : 'none';

        // 禁用生成按钮
        const button = document.getElementById('generateSummary');
        button.disabled = show;
        button.innerHTML = show ?
            '<i class="fas fa-spinner fa-spin"></i> AI生成中...' :
            '<i class="fas fa-magic"></i> 生成纪要';
    }

    // 创建AI增强的会议纪要
    createAIMeetingSummary(data) {
        const formatTime = (timeString) => {
            if (!timeString) return '未设置';
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN');
        };

        const modeNames = {
            'ai-concise': 'AI简洁版',
            'ai-detailed': 'AI详细版',
            'ai-action': 'AI行动项提取',
            'ai-custom': 'AI自定义'
        };

        return `
            <div class="summary-content">
                <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px;">
                    📋 ${data.title}
                </h4>

                <div style="margin-bottom: 20px;">
                    <p><strong>🕐 会议时间：</strong>${formatTime(data.time)}</p>
                    <p><strong>👥 参会人员：</strong>${data.participants || '未填写'}</p>
                    <p><strong>🤖 生成模式：</strong>${modeNames[data.mode] || data.mode}</p>
                </div>

                <div style="margin-bottom: 20px;">
                    <h5 style="color: #9b59b6; margin-bottom: 10px;">🤖 AI智能总结</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #9b59b6; white-space: pre-wrap;">
                        ${data.aiSummary.replace(/\n/g, '<br>')}
                    </div>
                </div>

                ${data.transcription ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #27ae60; margin-bottom: 10px;">🎤 语音转录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #27ae60;">
                        ${data.transcription.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}

                ${data.manualNotes ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #e74c3c; margin-bottom: 10px;">✏️ 手动记录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #e74c3c;">
                        ${data.manualNotes.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}

                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 12px;">
                    📝 纪要生成时间：${new Date().toLocaleString('zh-CN')} | 🤖 AI增强处理
                </div>
            </div>
        `;
    }

    // 创建会议纪要HTML
    createMeetingSummary(data) {
        const formatTime = (timeString) => {
            if (!timeString) return '未设置';
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN');
        };
        
        return `
            <div class="summary-content">
                <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px;">
                    📋 ${data.title}
                </h4>
                
                <div style="margin-bottom: 20px;">
                    <p><strong>🕐 会议时间：</strong>${formatTime(data.time)}</p>
                    <p><strong>👥 参会人员：</strong>${data.participants || '未填写'}</p>
                </div>
                
                ${data.transcription ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #27ae60; margin-bottom: 10px;">🎤 语音转录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #27ae60;">
                        ${data.transcription.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}
                
                ${data.manualNotes ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #e74c3c; margin-bottom: 10px;">✏️ 手动记录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #e74c3c;">
                        ${data.manualNotes.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}
                
                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 12px;">
                    📝 纪要生成时间：${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
    }

    // 保存会议记录
    saveMeeting() {
        const meetingData = {
            id: Date.now(),
            title: document.getElementById('meetingTitle').value || '未命名会议',
            time: document.getElementById('meetingTime').value,
            participants: document.getElementById('participants').value,
            transcription: this.transcriptionText,
            manualNotes: document.getElementById('manualNotes').value,
            summary: document.getElementById('meetingSummary').innerHTML,
            savedAt: new Date().toISOString()
        };
        
        if (!meetingData.transcription.trim() && !meetingData.manualNotes.trim()) {
            this.showModal('没有内容可保存，请先录制语音或输入笔记。');
            return;
        }
        
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        savedMeetings.unshift(meetingData);
        
        // 只保留最近50条记录
        if (savedMeetings.length > 50) {
            savedMeetings.splice(50);
        }
        
        localStorage.setItem('meetingNotes', JSON.stringify(savedMeetings));
        this.showModal('会议记录已保存成功！');
        this.loadSavedMeetings();
    }

    // 导出会议记录
    exportMeeting() {
        const summary = document.getElementById('meetingSummary').innerHTML;
        if (!summary || summary.includes('placeholder-text')) {
            this.showModal('请先生成会议纪要后再导出。');
            return;
        }
        
        const meetingTitle = document.getElementById('meetingTitle').value || '会议记录';
        const exportContent = this.createExportContent();
        
        const blob = new Blob([exportContent], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${meetingTitle}_${new Date().toISOString().slice(0, 10)}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showModal('会议记录已导出成功！');
    }

    // 创建导出内容
    createExportContent() {
        const summary = document.getElementById('meetingSummary').innerHTML;
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议记录 - Jason's Meeting Note</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; line-height: 1.6; margin: 40px; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { max-width: 800px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📝 Jason's Meeting Note</h1>
        <p>智能会议记录系统导出文档</p>
    </div>
    <div class="content">
        ${summary}
    </div>
</body>
</html>
        `;
    }

    // 新建会议
    newMeeting() {
        if (confirm('确定要新建会议吗？当前未保存的内容将会丢失。')) {
            document.getElementById('meetingTitle').value = '';
            document.getElementById('participants').value = '';
            this.clearTranscription();
            this.clearManualNotes();
            document.getElementById('meetingSummary').innerHTML = '<p class="placeholder-text">点击"生成纪要"按钮，系统将自动整合语音转录和手动笔记，生成结构化的会议纪要...</p>';
            this.setDefaultDateTime();
            this.stopRecording();
        }
    }

    // 加载保存的会议
    loadSavedMeetings() {
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        const historyList = document.getElementById('historyList');
        
        if (savedMeetings.length === 0) {
            historyList.innerHTML = '<p class="placeholder-text">暂无历史会议记录</p>';
            return;
        }
        
        historyList.innerHTML = savedMeetings.map(meeting => `
            <div class="history-item" style="border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 10px; cursor: pointer;" 
                 onclick="meetingApp.loadMeetingData(${meeting.id})">
                <h5 style="margin: 0 0 5px 0; color: #2c3e50;">${meeting.title}</h5>
                <p style="margin: 0; color: #6c757d; font-size: 12px;">
                    保存时间: ${new Date(meeting.savedAt).toLocaleString('zh-CN')}
                </p>
            </div>
        `).join('');
    }

    // 加载特定会议数据
    loadMeetingData(meetingId) {
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        const meeting = savedMeetings.find(m => m.id === meetingId);
        
        if (meeting) {
            document.getElementById('meetingTitle').value = meeting.title;
            document.getElementById('meetingTime').value = meeting.time;
            document.getElementById('participants').value = meeting.participants;
            this.transcriptionText = meeting.transcription;
            document.getElementById('manualNotes').value = meeting.manualNotes;
            
            this.updateTranscriptionDisplay(meeting.transcription, '');
            if (meeting.summary) {
                document.getElementById('meetingSummary').innerHTML = meeting.summary;
            }
            
            this.toggleHistorySection();
            this.showModal('会议记录已加载成功！');
        }
    }

    // 切换历史记录显示
    toggleHistorySection() {
        const historySection = document.querySelector('.history-section');
        if (historySection.style.display === 'none' || !historySection.style.display) {
            historySection.style.display = 'block';
            this.loadSavedMeetings();
        } else {
            historySection.style.display = 'none';
        }
    }

    // 显示模态框
    showModal(message) {
        document.getElementById('modalMessage').innerHTML = message;
        document.getElementById('statusModal').style.display = 'block';
    }

    // 关闭模态框
    closeModal() {
        document.getElementById('statusModal').style.display = 'none';
    }
}

// 初始化应用
let meetingApp;
document.addEventListener('DOMContentLoaded', () => {
    meetingApp = new MeetingNoteApp();
});

// 全局函数供HTML调用
window.meetingApp = null;
document.addEventListener('DOMContentLoaded', () => {
    window.meetingApp = new MeetingNoteApp();
});
