// <PERSON>'s Meeting Note - 主要JavaScript功能
class MeetingNoteApp {
    constructor() {
        this.recognition = null;
        this.isRecording = false;
        this.isPaused = false;
        this.transcriptionText = '';
        this.currentMeeting = null;
        this.aiConfig = null;

        this.initializeApp();
        this.setupEventListeners();
        this.checkBrowserSupport();
        this.loadAIConfig();
    }

    // 初始化应用
    initializeApp() {
        console.log('Jason\'s Meeting Note 应用初始化中...');
        this.loadSavedMeetings();
        this.setDefaultDateTime();
    }

    // 检查浏览器支持
    checkBrowserSupport() {
        console.log('🔍 检查浏览器支持...');

        // 检查语音识别API
        const hasSpeechRecognition = ('webkitSpeechRecognition' in window) || ('SpeechRecognition' in window);
        console.log('🎤 语音识别API支持:', hasSpeechRecognition);

        // 检查媒体设备API
        const hasMediaDevices = navigator.mediaDevices && navigator.mediaDevices.getUserMedia;
        console.log('📱 媒体设备API支持:', hasMediaDevices);

        // 检查浏览器信息
        const userAgent = navigator.userAgent;
        const isChrome = userAgent.includes('Chrome');
        const isFirefox = userAgent.includes('Firefox');
        const isSafari = userAgent.includes('Safari') && !userAgent.includes('Chrome');
        const isEdge = userAgent.includes('Edge');

        console.log('🌐 浏览器信息:', {
            userAgent: userAgent,
            isChrome: isChrome,
            isFirefox: isFirefox,
            isSafari: isSafari,
            isEdge: isEdge
        });

        if (!hasSpeechRecognition) {
            console.error('❌ 浏览器不支持语音识别功能');
            this.showModal('抱歉，您的浏览器不支持语音识别功能。\n\n推荐使用以下浏览器：\n• Chrome 25+\n• Edge 79+\n• Safari 14.1+');

            const startButton = document.getElementById('startRecording');
            if (startButton) {
                startButton.disabled = true;
                startButton.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 不支持';
            }
            return false;
        }

        if (!hasMediaDevices) {
            console.warn('⚠️ 浏览器不支持媒体设备API，可能影响麦克风访问');
        }

        console.log('✅ 浏览器支持检查通过');
        return true;
    }

    // 设置默认日期时间
    setDefaultDateTime() {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
            .toISOString().slice(0, 16);
        document.getElementById('meetingTime').value = localDateTime;
    }

    // 设置事件监听器
    setupEventListeners() {
        console.log('🔧 设置事件监听器...');

        try {
            // 语音控制按钮
            this.addEventListenerSafely('startRecording', 'click', () => this.startRecording());
            this.addEventListenerSafely('stopRecording', 'click', () => this.stopRecording());
            this.addEventListenerSafely('pauseRecording', 'click', () => this.pauseRecording());

            // 清空按钮
            this.addEventListenerSafely('clearTranscription', 'click', () => this.clearTranscription());
            this.addEventListenerSafely('clearManualNotes', 'click', () => this.clearManualNotes());

            // 时间戳按钮
            this.addEventListenerSafely('addTimestamp', 'click', () => this.addTimestamp());

            // 生成纪要按钮
            this.addEventListenerSafely('generateSummary', 'click', () => this.generateSummary());

            // 操作按钮
            this.addEventListenerSafely('saveMeeting', 'click', () => this.saveMeeting());
            this.addEventListenerSafely('exportMeeting', 'click', () => this.exportMeeting());
            this.addEventListenerSafely('newMeeting', 'click', () => this.newMeeting());
            this.addEventListenerSafely('loadMeeting', 'click', () => this.toggleHistorySection());

            // AI设置相关
            this.addEventListenerSafely('toggleAISettings', 'click', () => this.toggleAISettings());
            this.addEventListenerSafely('aiProvider', 'change', (e) => this.onProviderChange(e.target.value));
            this.addEventListenerSafely('toggleApiKey', 'click', () => this.toggleApiKeyVisibility());
            this.addEventListenerSafely('temperature', 'input', (e) => this.updateTemperatureDisplay(e.target.value));
            this.addEventListenerSafely('testConnection', 'click', () => this.testAPIConnection());
            this.addEventListenerSafely('saveAIConfig', 'click', () => this.saveAIConfig());
            this.addEventListenerSafely('clearAIConfig', 'click', () => this.clearAIConfig());

            // 模态框关闭
            const closeButton = document.querySelector('.close');
            if (closeButton) {
                closeButton.addEventListener('click', () => this.closeModal());
                console.log('✅ 模态框关闭按钮事件已绑定');
            } else {
                console.warn('⚠️ 未找到模态框关闭按钮');
            }

            window.addEventListener('click', (event) => {
                const modal = document.getElementById('statusModal');
                if (event.target === modal) {
                    this.closeModal();
                }
            });

            console.log('✅ 所有事件监听器设置完成');

        } catch (error) {
            console.error('❌ 设置事件监听器失败:', error);
            throw error;
        }
    }

    // 安全地添加事件监听器
    addEventListenerSafely(elementId, eventType, handler) {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener(eventType, handler);
            console.log(`✅ ${elementId} ${eventType} 事件已绑定`);
        } else {
            console.warn(`⚠️ 未找到元素: ${elementId}`);
        }
    }

    // 开始录音
    async startRecording() {
        console.log('🎤 开始录音按钮被点击');

        if (!this.checkBrowserSupport()) {
            console.error('❌ 浏览器不支持语音识别');
            return;
        }

        // 检查麦克风权限
        try {
            console.log('🔍 检查麦克风权限...');

            // 尝试获取系统音频（实验性功能）
            let audioConstraints = { audio: true };

            // 检查是否支持系统音频捕获
            if (navigator.mediaDevices.getDisplayMedia) {
                try {
                    console.log('🔊 尝试获取系统音频权限...');
                    // 注意：这需要用户手动选择共享音频
                    audioConstraints = {
                        audio: {
                            echoCancellation: false,
                            noiseSuppression: false,
                            autoGainControl: false
                        }
                    };
                } catch (displayError) {
                    console.log('ℹ️ 系统音频不可用，使用麦克风音频');
                }
            }

            const stream = await navigator.mediaDevices.getUserMedia(audioConstraints);
            console.log('✅ 音频权限获取成功');
            console.log('🎵 音频轨道信息:', stream.getAudioTracks().map(track => ({
                label: track.label,
                kind: track.kind,
                enabled: track.enabled
            })));

            // 停止测试流
            stream.getTracks().forEach(track => track.stop());
        } catch (error) {
            console.error('❌ 音频权限获取失败:', error);

            let errorMessage = '无法访问音频设备，请检查浏览器权限设置。';

            if (error.name === 'NotAllowedError') {
                errorMessage += '\n\n请点击地址栏的麦克风图标，选择"允许"。';
            } else if (error.name === 'NotFoundError') {
                errorMessage += '\n\n未找到可用的音频设备，请检查麦克风连接。';
            } else if (error.name === 'NotReadableError') {
                errorMessage += '\n\n音频设备被其他应用占用，请关闭其他使用麦克风的程序。';
            }

            errorMessage += '\n\n错误详情: ' + error.message;
            this.showModal(errorMessage);
            return;
        }

        try {
            console.log('🚀 初始化语音识别...');
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();

            // 配置语音识别
            this.recognition.continuous = true;
            this.recognition.interimResults = true;
            this.recognition.lang = 'zh-CN'; // 默认中文，可以根据需要调整
            this.recognition.maxAlternatives = 1;

            console.log('⚙️ 语音识别配置完成:', {
                continuous: this.recognition.continuous,
                interimResults: this.recognition.interimResults,
                lang: this.recognition.lang
            });

            // 语音识别事件处理
            this.recognition.onstart = () => {
                console.log('✅ 语音识别已开始');
                this.isRecording = true;
                this.isPaused = false;
                this.updateRecordingUI();
            };

            this.recognition.onresult = (event) => {
                let interimTranscript = '';
                let finalTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    const confidence = event.results[i][0].confidence;

                    console.log(`📝 识别结果 [${i}]: "${transcript}" (置信度: ${confidence})`);

                    if (event.results[i].isFinal) {
                        finalTranscript += transcript + ' ';
                    } else {
                        interimTranscript += transcript;
                    }
                }

                this.updateTranscriptionDisplay(finalTranscript, interimTranscript);
            };

            this.recognition.onerror = (event) => {
                console.error('❌ 语音识别错误:', event.error);
                const errorMessages = {
                    'no-speech': '未检测到语音，请确保麦克风正常工作',
                    'audio-capture': '音频捕获失败，请检查麦克风设备',
                    'not-allowed': '麦克风权限被拒绝，请在浏览器设置中允许麦克风访问',
                    'network': '网络错误，请检查网络连接',
                    'service-not-allowed': '语音识别服务不可用',
                    'bad-grammar': '语法错误',
                    'language-not-supported': '不支持的语言'
                };

                const userMessage = errorMessages[event.error] || `语音识别出现错误: ${event.error}`;
                this.showModal(userMessage);
                this.stopRecording();
            };

            this.recognition.onend = () => {
                console.log('🔄 语音识别结束');
                if (this.isRecording && !this.isPaused) {
                    // 如果还在录音状态但识别结束了，重新开始
                    console.log('🔄 自动重启语音识别...');
                    setTimeout(() => {
                        if (this.isRecording) {
                            this.recognition.start();
                        }
                    }, 100);
                }
            };

            console.log('🎯 启动语音识别...');
            this.recognition.start();

        } catch (error) {
            console.error('❌ 启动语音识别失败:', error);
            this.showModal('启动语音识别失败，请检查麦克风权限。\n\n错误详情: ' + error.message);
        }
    }

    // 停止录音
    stopRecording() {
        if (this.recognition) {
            this.isRecording = false;
            this.isPaused = false;
            this.recognition.stop();
            this.updateRecordingUI();
            console.log('语音识别已停止');
        }
    }

    // 暂停录音
    pauseRecording() {
        if (this.recognition && this.isRecording) {
            this.isPaused = !this.isPaused;
            
            if (this.isPaused) {
                this.recognition.stop();
            } else {
                this.recognition.start();
            }
            
            this.updateRecordingUI();
        }
    }

    // 更新录音UI状态
    updateRecordingUI() {
        const startBtn = document.getElementById('startRecording');
        const stopBtn = document.getElementById('stopRecording');
        const pauseBtn = document.getElementById('pauseRecording');
        const status = document.getElementById('recordingStatus');
        
        if (this.isRecording) {
            if (this.isPaused) {
                startBtn.disabled = false;
                stopBtn.disabled = false;
                pauseBtn.disabled = false;
                pauseBtn.innerHTML = '<i class="fas fa-play"></i> 继续';
                status.textContent = '已暂停';
                status.className = 'status-indicator paused';
            } else {
                startBtn.disabled = true;
                stopBtn.disabled = false;
                pauseBtn.disabled = false;
                pauseBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
                status.textContent = '正在录音';
                status.className = 'status-indicator recording';
            }
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            pauseBtn.disabled = true;
            pauseBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
            status.textContent = '准备就绪';
            status.className = 'status-indicator';
        }
    }

    // 更新转录显示
    updateTranscriptionDisplay(finalText, interimText) {
        const transcriptionDiv = document.getElementById('transcriptionText');
        
        if (finalText) {
            this.transcriptionText += finalText;
        }
        
        const displayText = this.transcriptionText + (interimText ? `<span style="color: #999; font-style: italic;">${interimText}</span>` : '');
        
        if (displayText.trim()) {
            transcriptionDiv.innerHTML = `<div style="white-space: pre-wrap;">${displayText}</div>`;
        } else {
            transcriptionDiv.innerHTML = '<p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>';
        }
        
        // 自动滚动到底部
        transcriptionDiv.scrollTop = transcriptionDiv.scrollHeight;
    }

    // 清空转录内容
    clearTranscription() {
        this.transcriptionText = '';
        document.getElementById('transcriptionText').innerHTML = '<p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>';
    }

    // 清空手动笔记
    clearManualNotes() {
        document.getElementById('manualNotes').value = '';
    }

    // 添加时间戳
    addTimestamp() {
        const textarea = document.getElementById('manualNotes');
        const now = new Date();
        const timestamp = `[${now.toLocaleTimeString()}] `;
        
        const cursorPos = textarea.selectionStart;
        const textBefore = textarea.value.substring(0, cursorPos);
        const textAfter = textarea.value.substring(cursorPos);
        
        textarea.value = textBefore + timestamp + textAfter;
        textarea.focus();
        textarea.setSelectionRange(cursorPos + timestamp.length, cursorPos + timestamp.length);
    }

    // 生成会议纪要
    async generateSummary() {
        const meetingTitle = document.getElementById('meetingTitle').value || '未命名会议';
        const meetingTime = document.getElementById('meetingTime').value;
        const participants = document.getElementById('participants').value;
        const transcription = this.transcriptionText;
        const manualNotes = document.getElementById('manualNotes').value;
        const summaryMode = document.getElementById('summaryMode').value;

        if (!transcription.trim() && !manualNotes.trim()) {
            this.showModal('请先录制语音或输入手动笔记后再生成纪要。');
            return;
        }

        const meetingData = {
            title: meetingTitle,
            time: meetingTime,
            participants: participants,
            transcription: transcription,
            manualNotes: manualNotes
        };

        if (summaryMode === 'basic') {
            // 基础整理模式
            const summary = this.createMeetingSummary(meetingData);
            document.getElementById('meetingSummary').innerHTML = summary;
        } else {
            // AI处理模式
            await this.generateAISummary(meetingData, summaryMode);
        }
    }

    // 生成AI会议纪要
    async generateAISummary(meetingData, mode) {
        if (!this.aiConfig || !this.aiConfig.apiKey) {
            this.showModal('请先配置AI设置后再使用AI生成功能。');
            return;
        }

        // 显示处理状态
        this.showAIProcessingStatus(true);

        try {
            // 准备AI处理的内容
            const content = this.prepareContentForAI(meetingData, mode);

            // 调用AI API
            const aiResponse = await this.callAIAPI(
                this.aiConfig.provider,
                this.aiConfig.apiKey,
                this.aiConfig.baseUrl,
                this.aiConfig.model,
                content,
                {
                    temperature: this.aiConfig.temperature,
                    max_tokens: this.aiConfig.maxTokens
                }
            );

            // 创建AI增强的纪要
            const enhancedSummary = this.createAIEnhancedSummary(meetingData, aiResponse, mode);
            document.getElementById('meetingSummary').innerHTML = enhancedSummary;

        } catch (error) {
            console.error('AI生成纪要失败:', error);
            this.showModal(`AI生成失败: ${error.message}`);

            // 降级到基础模式
            const basicSummary = this.createMeetingSummary(meetingData);
            document.getElementById('meetingSummary').innerHTML = basicSummary;

        } finally {
            this.showAIProcessingStatus(false);
        }
    }

    // 准备AI处理的内容
    prepareContentForAI(meetingData, mode) {
        let prompt = this.getPromptTemplate(mode);

        // 如果有自定义prompt，使用自定义的
        if (this.aiConfig.customPrompt && this.aiConfig.customPrompt.trim()) {
            prompt = this.aiConfig.customPrompt;
        }

        let content = `${prompt}\n\n`;
        content += `会议主题: ${meetingData.title}\n`;

        if (meetingData.time) {
            const date = new Date(meetingData.time);
            content += `会议时间: ${date.toLocaleString('zh-CN')}\n`;
        }

        if (meetingData.participants) {
            content += `参会人员: ${meetingData.participants}\n`;
        }

        content += '\n';

        if (meetingData.transcription.trim()) {
            content += '语音转录内容:\n' + meetingData.transcription + '\n\n';
        }

        if (meetingData.manualNotes.trim()) {
            content += '手动记录内容:\n' + meetingData.manualNotes + '\n';
        }

        return content;
    }

    // 获取不同模式的Prompt模板
    getPromptTemplate(mode) {
        const templates = {
            'ai-concise': `请将以下会议内容整理成简洁的会议纪要，要求：
1. 提取关键信息和重要决策
2. 使用简洁明了的语言
3. 按照逻辑顺序组织内容
4. 突出重点和结论`,

            'ai-detailed': `请将以下会议内容整理成详细的会议纪要，要求：
1. 完整记录讨论过程和要点
2. 详细描述各方观点和建议
3. 记录具体的数据和时间节点
4. 包含背景信息和上下文
5. 结构化呈现，便于查阅`,

            'ai-action': `请从以下会议内容中提取行动项和任务安排，要求：
1. 明确列出所有行动项
2. 标注负责人和截止时间
3. 按优先级排序
4. 包含具体的执行步骤
5. 标记依赖关系和风险点`
        };

        return templates[mode] || templates['ai-concise'];
    }

    // 创建AI增强的会议纪要
    createAIEnhancedSummary(meetingData, aiResponse, mode) {
        const formatTime = (timeString) => {
            if (!timeString) return '未设置';
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN');
        };

        const modeNames = {
            'ai-concise': 'AI简洁版',
            'ai-detailed': 'AI详细版',
            'ai-action': 'AI行动项提取'
        };

        return `
            <div class="summary-content ai-enhanced">
                <div class="ai-badge">
                    <i class="fas fa-robot"></i> ${modeNames[mode] || 'AI生成'}
                </div>

                <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #9b59b6; padding-bottom: 5px;">
                    📋 ${meetingData.title}
                </h4>

                <div style="margin-bottom: 20px;">
                    <p><strong>🕐 会议时间：</strong>${formatTime(meetingData.time)}</p>
                    <p><strong>👥 参会人员：</strong>${meetingData.participants || '未填写'}</p>
                    <p><strong>🤖 AI处理模式：</strong>${modeNames[mode] || mode}</p>
                </div>

                <div style="margin-bottom: 20px;">
                    <h5 style="color: #9b59b6; margin-bottom: 10px;">🤖 AI智能纪要</h5>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #9b59b6; white-space: pre-wrap; line-height: 1.8;">
                        ${aiResponse}
                    </div>
                </div>

                ${meetingData.transcription ? `
                <details style="margin-bottom: 15px;">
                    <summary style="cursor: pointer; color: #27ae60; font-weight: 500; margin-bottom: 10px;">
                        🎤 查看原始语音转录
                    </summary>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #27ae60; margin-top: 10px;">
                        ${meetingData.transcription.replace(/\n/g, '<br>')}
                    </div>
                </details>
                ` : ''}

                ${meetingData.manualNotes ? `
                <details style="margin-bottom: 15px;">
                    <summary style="cursor: pointer; color: #e74c3c; font-weight: 500; margin-bottom: 10px;">
                        ✏️ 查看手动记录
                    </summary>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #e74c3c; margin-top: 10px;">
                        ${meetingData.manualNotes.replace(/\n/g, '<br>')}
                    </div>
                </details>
                ` : ''}

                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 12px;">
                    📝 纪要生成时间：${new Date().toLocaleString('zh-CN')} |
                    🤖 AI模型：${this.aiConfig.model} |
                    🔧 服务商：${this.aiConfig.provider}
                </div>
            </div>
        `;
    }

    // 显示/隐藏AI处理状态
    showAIProcessingStatus(show) {
        const statusDiv = document.getElementById('aiProcessingStatus');
        const generateButton = document.getElementById('generateSummary');

        if (show) {
            statusDiv.style.display = 'block';
            generateButton.disabled = true;
            generateButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> AI处理中';

            // 更新处理消息
            const messages = [
                'AI正在分析会议内容...',
                'AI正在提取关键信息...',
                'AI正在生成结构化纪要...',
                'AI正在优化输出格式...'
            ];

            let messageIndex = 0;
            const messageInterval = setInterval(() => {
                if (statusDiv.style.display === 'none') {
                    clearInterval(messageInterval);
                    return;
                }

                document.getElementById('processingMessage').textContent = messages[messageIndex];
                messageIndex = (messageIndex + 1) % messages.length;
            }, 2000);

        } else {
            statusDiv.style.display = 'none';
            generateButton.disabled = false;
            generateButton.innerHTML = '<i class="fas fa-magic"></i> 生成纪要';
        }
    }
}

    // 创建会议纪要HTML
    createMeetingSummary(data) {
        const formatTime = (timeString) => {
            if (!timeString) return '未设置';
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN');
        };
        
        return `
            <div class="summary-content">
                <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px;">
                    📋 ${data.title}
                </h4>
                
                <div style="margin-bottom: 20px;">
                    <p><strong>🕐 会议时间：</strong>${formatTime(data.time)}</p>
                    <p><strong>👥 参会人员：</strong>${data.participants || '未填写'}</p>
                </div>
                
                ${data.transcription ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #27ae60; margin-bottom: 10px;">🎤 语音转录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #27ae60;">
                        ${data.transcription.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}
                
                ${data.manualNotes ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #e74c3c; margin-bottom: 10px;">✏️ 手动记录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #e74c3c;">
                        ${data.manualNotes.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}
                
                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 12px;">
                    📝 纪要生成时间：${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
    }

    // 保存会议记录
    saveMeeting() {
        const meetingData = {
            id: Date.now(),
            title: document.getElementById('meetingTitle').value || '未命名会议',
            time: document.getElementById('meetingTime').value,
            participants: document.getElementById('participants').value,
            transcription: this.transcriptionText,
            manualNotes: document.getElementById('manualNotes').value,
            summary: document.getElementById('meetingSummary').innerHTML,
            savedAt: new Date().toISOString()
        };
        
        if (!meetingData.transcription.trim() && !meetingData.manualNotes.trim()) {
            this.showModal('没有内容可保存，请先录制语音或输入笔记。');
            return;
        }
        
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        savedMeetings.unshift(meetingData);
        
        // 只保留最近50条记录
        if (savedMeetings.length > 50) {
            savedMeetings.splice(50);
        }
        
        localStorage.setItem('meetingNotes', JSON.stringify(savedMeetings));
        this.showModal('会议记录已保存成功！');
        this.loadSavedMeetings();
    }

    // 导出会议记录
    exportMeeting() {
        const summary = document.getElementById('meetingSummary').innerHTML;
        if (!summary || summary.includes('placeholder-text')) {
            this.showModal('请先生成会议纪要后再导出。');
            return;
        }
        
        const meetingTitle = document.getElementById('meetingTitle').value || '会议记录';
        const exportContent = this.createExportContent();
        
        const blob = new Blob([exportContent], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${meetingTitle}_${new Date().toISOString().slice(0, 10)}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showModal('会议记录已导出成功！');
    }

    // 创建导出内容
    createExportContent() {
        const summary = document.getElementById('meetingSummary').innerHTML;
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议记录 - Jason's Meeting Note</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; line-height: 1.6; margin: 40px; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { max-width: 800px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📝 Jason's Meeting Note</h1>
        <p>智能会议记录系统导出文档</p>
    </div>
    <div class="content">
        ${summary}
    </div>
</body>
</html>
        `;
    }

    // 新建会议
    newMeeting() {
        if (confirm('确定要新建会议吗？当前未保存的内容将会丢失。')) {
            document.getElementById('meetingTitle').value = '';
            document.getElementById('participants').value = '';
            this.clearTranscription();
            this.clearManualNotes();
            document.getElementById('meetingSummary').innerHTML = '<p class="placeholder-text">点击"生成纪要"按钮，系统将自动整合语音转录和手动笔记，生成结构化的会议纪要...</p>';
            this.setDefaultDateTime();
            this.stopRecording();
        }
    }

    // 加载保存的会议
    loadSavedMeetings() {
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        const historyList = document.getElementById('historyList');
        
        if (savedMeetings.length === 0) {
            historyList.innerHTML = '<p class="placeholder-text">暂无历史会议记录</p>';
            return;
        }
        
        historyList.innerHTML = savedMeetings.map(meeting => `
            <div class="history-item" style="border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 10px; cursor: pointer;" 
                 onclick="meetingApp.loadMeetingData(${meeting.id})">
                <h5 style="margin: 0 0 5px 0; color: #2c3e50;">${meeting.title}</h5>
                <p style="margin: 0; color: #6c757d; font-size: 12px;">
                    保存时间: ${new Date(meeting.savedAt).toLocaleString('zh-CN')}
                </p>
            </div>
        `).join('');
    }

    // 加载特定会议数据
    loadMeetingData(meetingId) {
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        const meeting = savedMeetings.find(m => m.id === meetingId);
        
        if (meeting) {
            document.getElementById('meetingTitle').value = meeting.title;
            document.getElementById('meetingTime').value = meeting.time;
            document.getElementById('participants').value = meeting.participants;
            this.transcriptionText = meeting.transcription;
            document.getElementById('manualNotes').value = meeting.manualNotes;
            
            this.updateTranscriptionDisplay(meeting.transcription, '');
            if (meeting.summary) {
                document.getElementById('meetingSummary').innerHTML = meeting.summary;
            }
            
            this.toggleHistorySection();
            this.showModal('会议记录已加载成功！');
        }
    }

    // 切换历史记录显示
    toggleHistorySection() {
        const historySection = document.querySelector('.history-section');
        if (historySection.style.display === 'none' || !historySection.style.display) {
            historySection.style.display = 'block';
            this.loadSavedMeetings();
        } else {
            historySection.style.display = 'none';
        }
    }

    // 显示模态框
    showModal(message) {
        document.getElementById('modalMessage').innerHTML = message;
        document.getElementById('statusModal').style.display = 'block';
    }

    // 关闭模态框
    closeModal() {
        document.getElementById('statusModal').style.display = 'none';
    }

    // ==================== AI配置管理方法 ====================

    // 切换AI设置面板显示
    toggleAISettings() {
        console.log('🔧 切换AI设置面板');

        const panel = document.getElementById('aiSettingsPanel');
        const button = document.getElementById('toggleAISettings');

        if (!panel) {
            console.error('❌ 未找到AI设置面板元素 (aiSettingsPanel)');
            this.showModal('AI设置面板加载失败，请刷新页面重试');
            return;
        }

        if (!button) {
            console.error('❌ 未找到AI设置按钮元素 (toggleAISettings)');
            return;
        }

        console.log('📊 当前面板状态:', {
            display: panel.style.display,
            computed: window.getComputedStyle(panel).display,
            visible: panel.offsetHeight > 0
        });

        if (panel.style.display === 'none' || !panel.style.display) {
            console.log('👁️ 显示AI设置面板');
            panel.style.display = 'block';
            button.innerHTML = '<i class="fas fa-chevron-up"></i> 收起设置';

            // 确保面板可见
            setTimeout(() => {
                const isVisible = panel.offsetHeight > 0;
                console.log('✅ 面板显示状态检查:', isVisible);
                if (!isVisible) {
                    console.warn('⚠️ 面板可能未正确显示');
                }
            }, 100);
        } else {
            console.log('👁️‍🗨️ 隐藏AI设置面板');
            panel.style.display = 'none';
            button.innerHTML = '<i class="fas fa-cog"></i> 配置AI';
        }
    }

    // 加载AI配置
    loadAIConfig() {
        const savedConfig = localStorage.getItem('aiConfig');
        if (savedConfig) {
            try {
                this.aiConfig = JSON.parse(savedConfig);
                this.populateAIConfigForm();
            } catch (error) {
                console.error('加载AI配置失败:', error);
                this.aiConfig = null;
            }
        }
    }

    // 填充AI配置表单
    populateAIConfigForm() {
        if (!this.aiConfig) return;

        document.getElementById('aiProvider').value = this.aiConfig.provider || '';
        document.getElementById('apiKey').value = this.aiConfig.apiKey || '';
        document.getElementById('apiBaseUrl').value = this.aiConfig.baseUrl || '';
        document.getElementById('modelName').value = this.aiConfig.model || '';
        document.getElementById('temperature').value = this.aiConfig.temperature || 0.7;
        document.getElementById('maxTokens').value = this.aiConfig.maxTokens || 2000;
        document.getElementById('customPrompt').value = this.aiConfig.customPrompt || '';

        this.updateTemperatureDisplay(this.aiConfig.temperature || 0.7);

        if (this.aiConfig.provider) {
            this.onProviderChange(this.aiConfig.provider);
        }
    }

    // AI服务商变更处理
    onProviderChange(provider) {
        const configSection = document.getElementById('apiConfigSection');
        const baseUrlInput = document.getElementById('apiBaseUrl');
        const modelSelect = document.getElementById('modelName');

        if (provider) {
            configSection.style.display = 'block';

            // 设置默认Base URL和可用模型
            const providerConfigs = this.getProviderConfigs();
            const config = providerConfigs[provider];

            if (config) {
                baseUrlInput.value = config.baseUrl;
                baseUrlInput.placeholder = config.baseUrl;

                // 更新模型选项
                modelSelect.innerHTML = '<option value="">请选择模型</option>';
                config.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.value;
                    option.textContent = model.name;
                    modelSelect.appendChild(option);
                });
            }
        } else {
            configSection.style.display = 'none';
        }
    }

    // 获取各服务商的配置信息
    getProviderConfigs() {
        return {
            deepseek: {
                baseUrl: 'https://api.deepseek.com/v1',
                models: [
                    { value: 'deepseek-chat', name: 'DeepSeek Chat' },
                    { value: 'deepseek-coder', name: 'DeepSeek Coder' }
                ]
            },
            minimax: {
                baseUrl: 'https://api.minimax.chat/v1',
                models: [
                    { value: 'abab6.5s-chat', name: 'ABAB 6.5s Chat' },
                    { value: 'abab6.5-chat', name: 'ABAB 6.5 Chat' }
                ]
            },
            doubao: {
                baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
                models: [
                    { value: 'ep-20241218143820-lmvzr', name: '豆包 Pro' },
                    { value: 'ep-20241218143820-lmvzs', name: '豆包 Lite' }
                ]
            },
            gemini: {
                baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
                models: [
                    { value: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
                    { value: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' }
                ]
            },
            openai: {
                baseUrl: 'https://api.openai.com/v1',
                models: [
                    { value: 'gpt-4o', name: 'GPT-4o' },
                    { value: 'gpt-4o-mini', name: 'GPT-4o Mini' },
                    { value: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
                ]
            },
            custom: {
                baseUrl: '',
                models: [
                    { value: 'custom-model', name: '自定义模型' }
                ]
            }
        };
    }

    // 切换API Key显示/隐藏
    toggleApiKeyVisibility() {
        const apiKeyInput = document.getElementById('apiKey');
        const toggleButton = document.getElementById('toggleApiKey');

        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            toggleButton.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            apiKeyInput.type = 'password';
            toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
        }
    }

    // 更新温度显示
    updateTemperatureDisplay(value) {
        document.getElementById('temperatureValue').textContent = value;
    }

    // 测试API连接
    async testAPIConnection() {
        const provider = document.getElementById('aiProvider').value;
        const apiKey = document.getElementById('apiKey').value;
        const baseUrl = document.getElementById('apiBaseUrl').value;
        const model = document.getElementById('modelName').value;

        if (!provider || !apiKey || !baseUrl || !model) {
            this.showModal('请先完整填写AI配置信息');
            return;
        }

        const statusIndicator = document.getElementById('statusIndicator');
        const statusMessage = document.getElementById('statusMessage');
        const testButton = document.getElementById('testConnection');

        // 更新UI状态
        statusIndicator.className = 'status-indicator testing';
        statusIndicator.textContent = '测试中';
        statusMessage.textContent = '正在连接API...';
        testButton.disabled = true;
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中';

        try {
            const testResult = await this.performAPITest(provider, apiKey, baseUrl, model);

            if (testResult.success) {
                statusIndicator.className = 'status-indicator connected';
                statusIndicator.textContent = '连接成功';
                statusMessage.textContent = testResult.message || '连接正常，可以使用AI功能';
            } else {
                statusIndicator.className = 'status-indicator failed';
                statusIndicator.textContent = '连接失败';
                statusMessage.textContent = testResult.error || '连接失败，请检查配置';
            }
        } catch (error) {
            statusIndicator.className = 'status-indicator failed';
            statusIndicator.textContent = '连接失败';
            statusMessage.textContent = `错误: ${error.message}`;
        } finally {
            testButton.disabled = false;
            testButton.innerHTML = '<i class="fas fa-plug"></i> 测试连接';
        }
    }

    // 执行API测试
    async performAPITest(provider, apiKey, baseUrl, model) {
        const testMessage = "Hello, this is a connection test.";

        try {
            const response = await this.callAIAPI(provider, apiKey, baseUrl, model, testMessage, {
                temperature: 0.1,
                max_tokens: 50
            });

            return {
                success: true,
                message: `连接成功，模型响应正常 (${response.length} 字符)`
            };
        } catch (error) {
            return {
                success: false,
                error: this.parseAPIError(error)
            };
        }
    }

    // 解析API错误
    parseAPIError(error) {
        if (error.message.includes('401')) {
            return 'API Key无效或已过期';
        } else if (error.message.includes('403')) {
            return 'API访问被拒绝，请检查权限';
        } else if (error.message.includes('429')) {
            return 'API调用频率超限，请稍后重试';
        } else if (error.message.includes('500')) {
            return 'API服务器内部错误';
        } else if (error.message.includes('timeout')) {
            return '连接超时，请检查网络';
        } else if (error.message.includes('Failed to fetch')) {
            return '网络连接失败，请检查网络或API地址';
        } else {
            return error.message || '未知错误';
        }
    }

    // 保存AI配置
    saveAIConfig() {
        const config = {
            provider: document.getElementById('aiProvider').value,
            apiKey: document.getElementById('apiKey').value,
            baseUrl: document.getElementById('apiBaseUrl').value,
            model: document.getElementById('modelName').value,
            temperature: parseFloat(document.getElementById('temperature').value),
            maxTokens: parseInt(document.getElementById('maxTokens').value),
            customPrompt: document.getElementById('customPrompt').value,
            savedAt: new Date().toISOString()
        };

        if (!config.provider || !config.apiKey || !config.model) {
            this.showModal('请先完整填写必要的配置信息');
            return;
        }

        try {
            localStorage.setItem('aiConfig', JSON.stringify(config));
            this.aiConfig = config;
            this.showModal('AI配置已保存成功！');
        } catch (error) {
            console.error('保存AI配置失败:', error);
            this.showModal('保存配置失败，请重试');
        }
    }

    // 清空AI配置
    clearAIConfig() {
        if (confirm('确定要清空所有AI配置吗？此操作不可撤销。')) {
            localStorage.removeItem('aiConfig');
            this.aiConfig = null;

            // 重置表单
            document.getElementById('aiProvider').value = '';
            document.getElementById('apiKey').value = '';
            document.getElementById('apiBaseUrl').value = '';
            document.getElementById('modelName').innerHTML = '<option value="">请先选择AI服务商</option>';
            document.getElementById('temperature').value = 0.7;
            document.getElementById('maxTokens').value = 2000;
            document.getElementById('customPrompt').value = '';

            // 隐藏配置区域
            document.getElementById('apiConfigSection').style.display = 'none';

            // 重置连接状态
            const statusIndicator = document.getElementById('statusIndicator');
            const statusMessage = document.getElementById('statusMessage');
            statusIndicator.className = 'status-indicator';
            statusIndicator.textContent = '未测试';
            statusMessage.textContent = '';

            this.updateTemperatureDisplay(0.7);
            this.showModal('AI配置已清空');
        }
    }

    // ==================== AI API调用核心方法 ====================

    // 调用AI API的通用方法
    async callAIAPI(provider, apiKey, baseUrl, model, content, options = {}) {
        const timeout = options.timeout || 30000; // 30秒超时

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        try {
            let requestConfig;

            switch (provider) {
                case 'openai':
                case 'deepseek':
                case 'custom':
                    requestConfig = this.buildOpenAIRequest(apiKey, baseUrl, model, content, options);
                    break;
                case 'gemini':
                    requestConfig = this.buildGeminiRequest(apiKey, baseUrl, model, content, options);
                    break;
                case 'minimax':
                    requestConfig = this.buildMinimaxRequest(apiKey, baseUrl, model, content, options);
                    break;
                case 'doubao':
                    requestConfig = this.buildDoubaoRequest(apiKey, baseUrl, model, content, options);
                    break;
                default:
                    throw new Error(`不支持的AI服务商: ${provider}`);
            }

            const response = await fetch(requestConfig.url, {
                ...requestConfig.options,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return this.extractResponseContent(provider, data);

        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请检查网络连接');
            }
            throw error;
        }
    }

    // 构建OpenAI格式的请求
    buildOpenAIRequest(apiKey, baseUrl, model, content, options) {
        return {
            url: `${baseUrl}/chat/completions`,
            options: {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: model,
                    messages: [
                        {
                            role: 'user',
                            content: content
                        }
                    ],
                    temperature: options.temperature || 0.7,
                    max_tokens: options.max_tokens || 2000
                })
            }
        };
    }

    // 构建Gemini格式的请求
    buildGeminiRequest(apiKey, baseUrl, model, content, options) {
        return {
            url: `${baseUrl}/models/${model}:generateContent?key=${apiKey}`,
            options: {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: content
                        }]
                    }],
                    generationConfig: {
                        temperature: options.temperature || 0.7,
                        maxOutputTokens: options.max_tokens || 2000
                    }
                })
            }
        };
    }

    // 构建MiniMax格式的请求
    buildMinimaxRequest(apiKey, baseUrl, model, content, options) {
        return {
            url: `${baseUrl}/chat/completions`,
            options: {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: model,
                    messages: [
                        {
                            role: 'user',
                            content: content
                        }
                    ],
                    temperature: options.temperature || 0.7,
                    max_tokens: options.max_tokens || 2000
                })
            }
        };
    }

    // 构建豆包格式的请求
    buildDoubaoRequest(apiKey, baseUrl, model, content, options) {
        return {
            url: `${baseUrl}/chat/completions`,
            options: {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: model,
                    messages: [
                        {
                            role: 'user',
                            content: content
                        }
                    ],
                    temperature: options.temperature || 0.7,
                    max_tokens: options.max_tokens || 2000
                })
            }
        };
    }

    // 提取响应内容
    extractResponseContent(provider, data) {
        try {
            switch (provider) {
                case 'openai':
                case 'deepseek':
                case 'minimax':
                case 'doubao':
                case 'custom':
                    return data.choices[0].message.content;
                case 'gemini':
                    return data.candidates[0].content.parts[0].text;
                default:
                    throw new Error(`不支持的响应格式: ${provider}`);
            }
        } catch (error) {
            console.error('解析AI响应失败:', error, data);
            throw new Error('AI响应格式错误，请检查API配置');
        }
    }
}

// 初始化应用
let meetingApp;
window.meetingApp = null;

document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Jason\'s Meeting Note 正在初始化...');
    try {
        meetingApp = new MeetingNoteApp();
        window.meetingApp = meetingApp;
        console.log('✅ 应用初始化成功');
    } catch (error) {
        console.error('❌ 应用初始化失败:', error);
        alert('应用初始化失败，请刷新页面重试。错误信息: ' + error.message);
    }
});
