// Jason's Meeting Note - 主要JavaScript功能
class MeetingNoteApp {
    constructor() {
        this.recognition = null;
        this.isRecording = false;
        this.isPaused = false;
        this.transcriptionText = '';
        this.currentMeeting = null;
        this.aiConfig = null;
        this.audioSource = 'microphone'; // 默认使用麦克风
        this.systemAudioStream = null;

        this.initializeApp();
        this.setupEventListeners();
        this.checkBrowserSupport();
        this.loadAIConfig();
    }

    // 初始化应用
    initializeApp() {
        console.log('Jason\'s Meeting Note 应用初始化中...');
        this.loadSavedMeetings();
        this.setDefaultDateTime();

        // 延迟验证元素，确保DOM完全加载
        setTimeout(() => {
            const validation = this.validateAllElements();
            if (validation.completeness < 100) {
                console.warn(`⚠️ 元素完整性检查: ${validation.completeness}%`);

                // 在开发模式下显示调试面板
                if (window.location.protocol === 'file:' || window.location.hostname === 'localhost') {
                    this.showDebugPanel();
                }
            } else {
                console.log('✅ 所有界面元素验证通过');
            }
        }, 500);
    }

    // 检查浏览器支持
    checkBrowserSupport() {
        console.log('🔍 检查浏览器支持...');

        // 检查语音识别API
        const hasSpeechRecognition = ('webkitSpeechRecognition' in window) || ('SpeechRecognition' in window);
        console.log('🎤 语音识别API支持:', hasSpeechRecognition);

        // 检查媒体设备API
        const hasMediaDevices = navigator.mediaDevices && navigator.mediaDevices.getUserMedia;
        console.log('📱 媒体设备API支持:', hasMediaDevices);

        // 检查浏览器信息
        const userAgent = navigator.userAgent;
        const isChrome = userAgent.includes('Chrome');
        const isFirefox = userAgent.includes('Firefox');
        const isSafari = userAgent.includes('Safari') && !userAgent.includes('Chrome');
        const isEdge = userAgent.includes('Edge');

        console.log('🌐 浏览器信息:', {
            userAgent: userAgent,
            isChrome: isChrome,
            isFirefox: isFirefox,
            isSafari: isSafari,
            isEdge: isEdge
        });

        if (!hasSpeechRecognition) {
            console.error('❌ 浏览器不支持语音识别功能');
            this.showModal('抱歉，您的浏览器不支持语音识别功能。\n\n推荐使用以下浏览器：\n• Chrome 25+\n• Edge 79+\n• Safari 14.1+');

            const startButton = document.getElementById('startRecording');
            if (startButton) {
                startButton.disabled = true;
                startButton.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 不支持';
            }
            return false;
        }

        if (!hasMediaDevices) {
            console.warn('⚠️ 浏览器不支持媒体设备API，可能影响麦克风访问');
        }

        console.log('✅ 浏览器支持检查通过');
        return true;
    }

    // 设置默认日期时间
    setDefaultDateTime() {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
            .toISOString().slice(0, 16);
        document.getElementById('meetingTime').value = localDateTime;
    }

    // 设置事件监听器
    setupEventListeners() {
        console.log('🔧 设置事件监听器...');

        try {
            // 语音控制按钮
            this.addEventListenerSafely('startRecording', 'click', () => this.startRecording());
            this.addEventListenerSafely('stopRecording', 'click', () => this.stopRecording());
            this.addEventListenerSafely('pauseRecording', 'click', () => this.pauseRecording());

            // 清空按钮
            this.addEventListenerSafely('clearTranscription', 'click', () => this.clearTranscription());
            this.addEventListenerSafely('clearManualNotes', 'click', () => this.clearManualNotes());

            // 时间戳按钮
            this.addEventListenerSafely('addTimestamp', 'click', () => this.addTimestamp());

            // 生成纪要按钮
            this.addEventListenerSafely('generateSummary', 'click', () => this.generateSummary());

            // 操作按钮
            this.addEventListenerSafely('saveMeeting', 'click', () => this.saveMeeting());
            this.addEventListenerSafely('exportMeeting', 'click', () => this.exportMeeting());
            this.addEventListenerSafely('newMeeting', 'click', () => this.newMeeting());
            this.addEventListenerSafely('loadMeeting', 'click', () => this.toggleHistorySection());

            // AI设置相关
            this.addEventListenerSafely('toggleAISettings', 'click', () => this.toggleAISettings());
            this.addEventListenerSafely('aiProvider', 'change', (e) => this.onProviderChange(e.target.value));
            this.addEventListenerSafely('toggleApiKey', 'click', () => this.toggleApiKeyVisibility());
            this.addEventListenerSafely('temperature', 'input', (e) => this.updateTemperatureDisplay(e.target.value));
            this.addEventListenerSafely('testConnection', 'click', () => this.testAPIConnection());
            this.addEventListenerSafely('saveAIConfig', 'click', () => this.saveAIConfig());
            this.addEventListenerSafely('clearAIConfig', 'click', () => this.clearAIConfig());

            // 调试面板相关
            this.addEventListenerSafely('toggleDebugPanel', 'click', () => this.hideDebugPanel());

            // 模态框关闭
            const closeButton = document.querySelector('.close');
            if (closeButton) {
                closeButton.addEventListener('click', () => this.closeModal());
                console.log('✅ 模态框关闭按钮事件已绑定');
            } else {
                console.warn('⚠️ 未找到模态框关闭按钮');
            }

            window.addEventListener('click', (event) => {
                const modal = document.getElementById('statusModal');
                if (event.target === modal) {
                    this.closeModal();
                }
            });

            // 添加快捷键支持
            window.addEventListener('keydown', (event) => {
                // Ctrl+Shift+D 显示/隐藏调试面板
                if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                    event.preventDefault();
                    const debugPanel = document.getElementById('debugPanel');
                    if (debugPanel) {
                        if (debugPanel.style.display === 'none' || !debugPanel.style.display) {
                            this.showDebugPanel();
                        } else {
                            this.hideDebugPanel();
                        }
                    }
                }

                // Ctrl+Shift+V 运行元素验证
                if (event.ctrlKey && event.shiftKey && event.key === 'V') {
                    event.preventDefault();
                    this.validateAllElements();
                }
            });

            // 添加音频源选择器
            this.addEventListenerSafely('audioSourceMic', 'change', () => this.setAudioSource('microphone'));
            this.addEventListenerSafely('audioSourceSystem', 'change', () => this.setAudioSource('system'));
            this.addEventListenerSafely('audioSourceMixed', 'change', () => this.setAudioSource('mixed'));

            // 添加音频帮助按钮
            this.addEventListenerSafely('audioHelpBtn', 'click', () => this.showAudioHelp());

            // 手动笔记相关
            this.addEventListenerSafely('manualNotes', 'focus', () => this.handleNotesFocus());
            this.addEventListenerSafely('manualNotes', 'input', () => this.handleNotesInput());
            this.addEventListenerSafely('clearManualNotes', 'click', () => this.clearManualNotes());
            this.addEventListenerSafely('addTimestamp', 'click', () => this.addTimestamp());

            console.log('✅ 所有事件监听器设置完成');

        } catch (error) {
            console.error('❌ 设置事件监听器失败:', error);
            throw error;
        }
    }

    // 安全地添加事件监听器
    addEventListenerSafely(elementId, eventType, handler) {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener(eventType, handler);
            console.log(`✅ ${elementId} ${eventType} 事件已绑定`);
        } else {
            console.warn(`⚠️ 未找到元素: ${elementId}`);
        }
    }

    // 修复实时语音转录功能
    async startRecording() {
        console.log('🎤 开始录音按钮被点击');
        console.log(`🔊 当前音频源: ${this.audioSource}`);

        if (!this.checkBrowserSupport()) {
            this.showToast('您的浏览器不支持语音识别功能', 'error');
            return;
        }

        // 更新UI状态
        this.updateRecordingUI(true);

        try {
            // 根据音频源获取相应的音频流
            let audioStream = null;
            
            switch (this.audioSource) {
                case 'microphone':
                    audioStream = await this.getMicrophoneStream();
                    break;
                case 'system':
                    audioStream = await this.getSystemAudioStream();
                    break;
                case 'mixed':
                    audioStream = await this.getMixedAudioStream();
                    break;
                default:
                    audioStream = await this.getMicrophoneStream();
            }
            
            if (!audioStream) {
                this.updateRecordingUI(false);
                return;
            }
            
            // 保存系统音频流以便后续停止
            if (this.audioSource !== 'microphone') {
                this.systemAudioStream = audioStream;
            }
            
            // 创建音频可视化
            this.createAudioVisualizer(audioStream);
            
            // 启动语音识别
            await this.startSpeechRecognition();
            
        } catch (error) {
            console.error('❌ 音频处理失败:', error);
            this.showToast('音频处理失败: ' + error.message, 'error');
            this.updateRecordingUI(false);
        }
    }

    // 停止录音
    stopRecording() {
        console.log('⏹️ 停止录音');
        
        if (this.recognition) {
            this.recognition.stop();
            this.recognition = null;
        }
        
        // 停止系统音频流
        if (this.systemAudioStream) {
            this.systemAudioStream.getTracks().forEach(track => track.stop());
            this.systemAudioStream = null;
        }
        
        this.isRecording = false;
        this.isPaused = false;
        this.updateRecordingUI();
    }

    // 暂停/继续录音
    pauseRecording() {
        if (!this.isRecording) return;
        
        this.isPaused = !this.isPaused;
        
        if (this.isPaused) {
            // 暂停录音
            if (this.recognition) {
                this.recognition.stop();
            }
            this.showToast('录音已暂停', 'info');
        } else {
            // 继续录音
            if (this.recognition) {
                this.recognition.start();
            }
            this.showToast('录音已继续', 'info');
        }
        
        this.updateRecordingUI();
    }

    // 更新录音UI状态
    updateRecordingUI(isRecording) {
        const startBtn = document.getElementById('startRecording');
        const stopBtn = document.getElementById('stopRecording');
        const pauseBtn = document.getElementById('pauseRecording');
        const status = document.getElementById('recordingStatus');
        
        if (startBtn) startBtn.disabled = isRecording;
        if (stopBtn) stopBtn.disabled = !isRecording;
        if (pauseBtn) {
            pauseBtn.disabled = !isRecording;
            pauseBtn.innerHTML = this.isPaused ? 
                '<i class="fas fa-play"></i> 继续' : 
                '<i class="fas fa-pause"></i> 暂停';
        }
        
        if (status) {
            if (isRecording) {
                status.textContent = this.isPaused ? '已暂停' : '正在录音';
                status.className = 'status-indicator ' + 
                    (this.isPaused ? 'status-paused' : 'status-recording');
            } else {
                status.textContent = '准备就绪';
                status.className = 'status-indicator';
            }
        }
    }

    // 更新状态指示器
    updateStatusIndicator(message, type = 'info') {
        const status = document.getElementById('recordingStatus');
        if (status) {
            const originalText = status.textContent;
            status.textContent = message;
            status.className = `status-indicator status-${type}`;
            
            // 3秒后恢复原状态
            setTimeout(() => {
                status.textContent = originalText;
                status.className = this.isRecording ? 
                    (this.isPaused ? 'status-indicator status-paused' : 'status-indicator status-recording') : 
                    'status-indicator';
            }, 3000);
        }
    }

    // 改进转录显示方法
    updateTranscriptionDisplay(finalText, interimText) {
        console.log('🔄 更新转录显示:', { finalText, interimText });
        const transcriptionDiv = document.getElementById('transcriptionText');
        
        if (!transcriptionDiv) {
            console.error('❌ 未找到转录显示区域');
            return;
        }
        
        if (finalText) {
            this.transcriptionText += finalText;
            console.log('✅ 添加最终转录:', finalText);
        }
        
        const displayText = this.transcriptionText + 
            (interimText ? `<span class="interim-text">${interimText}</span>` : '');
        
        if (displayText.trim()) {
            transcriptionDiv.innerHTML = `<div class="transcription-content">${displayText}</div>`;
        } else {
            transcriptionDiv.innerHTML = '<p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>';
        }
        
        // 自动滚动到底部
        transcriptionDiv.scrollTop = transcriptionDiv.scrollHeight;
    }

    // 清空转录内容
    clearTranscription() {
        this.transcriptionText = '';
        document.getElementById('transcriptionText').innerHTML = '<p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>';
    }

    // 清空手动笔记
    clearManualNotes() {
        document.getElementById('manualNotes').value = '';
    }

    // 添加时间戳
    addTimestamp() {
        const textarea = document.getElementById('manualNotes');
        const now = new Date();
        const timestamp = `[${now.toLocaleTimeString()}] `;
        
        const cursorPos = textarea.selectionStart;
        const textBefore = textarea.value.substring(0, cursorPos);
        const textAfter = textarea.value.substring(cursorPos);
        
        textarea.value = textBefore + timestamp + textAfter;
        textarea.focus();
        textarea.setSelectionRange(cursorPos + timestamp.length, cursorPos + timestamp.length);
    }

    // 生成会议纪要
    async generateSummary() {
        const meetingTitle = document.getElementById('meetingTitle').value || '未命名会议';
        const meetingTime = document.getElementById('meetingTime').value;
        const participants = document.getElementById('participants').value;
        const transcription = this.transcriptionText;
        const manualNotes = document.getElementById('manualNotes').value;
        const summaryMode = document.getElementById('summaryMode').value;

        if (!transcription.trim() && !manualNotes.trim()) {
            this.showModal('请先录制语音或输入手动笔记后再生成纪要。');
            return;
        }

        const meetingData = {
            title: meetingTitle,
            time: meetingTime,
            participants: participants,
            transcription: transcription,
            manualNotes: manualNotes
        };

        if (summaryMode === 'basic') {
            // 基础整理模式
            const summary = this.createMeetingSummary(meetingData);
            document.getElementById('meetingSummary').innerHTML = summary;
        } else {
            // AI处理模式
            await this.generateAISummary(meetingData, summaryMode);
        }
    }

    // 生成AI会议纪要
    async generateAISummary(meetingData, mode) {
        if (!this.aiConfig || !this.aiConfig.apiKey) {
            this.showModal('请先配置AI设置后再使用AI生成功能。');
            return;
        }

        // 显示处理状态
        this.showAIProcessingStatus(true);

        try {
            // 准备AI处理的内容
            const content = this.prepareContentForAI(meetingData, mode);

            // 调用AI API
            const aiResponse = await this.callAIAPI(
                this.aiConfig.provider,
                this.aiConfig.apiKey,
                this.aiConfig.baseUrl,
                this.aiConfig.model,
                content,
                {
                    temperature: this.aiConfig.temperature,
                    max_tokens: this.aiConfig.maxTokens
                }
            );

            // 创建AI增强的纪要
            const enhancedSummary = this.createAIEnhancedSummary(meetingData, aiResponse, mode);
            document.getElementById('meetingSummary').innerHTML = enhancedSummary;

        } catch (error) {
            console.error('AI生成纪要失败:', error);
            this.showModal(`AI生成失败: ${error.message}`);

            // 降级到基础模式
            const basicSummary = this.createMeetingSummary(meetingData);
            document.getElementById('meetingSummary').innerHTML = basicSummary;

        } finally {
            this.showAIProcessingStatus(false);
        }
    }

    // 准备AI处理的内容
    prepareContentForAI(meetingData, mode) {
        let prompt = this.getPromptTemplate(mode);

        // 如果有自定义prompt，使用自定义的
        if (this.aiConfig.customPrompt && this.aiConfig.customPrompt.trim()) {
            prompt = this.aiConfig.customPrompt;
        }

        let content = `${prompt}\n\n`;
        content += `会议主题: ${meetingData.title}\n`;

        if (meetingData.time) {
            const date = new Date(meetingData.time);
            content += `会议时间: ${date.toLocaleString('zh-CN')}\n`;
        }

        if (meetingData.participants) {
            content += `参会人员: ${meetingData.participants}\n`;
        }

        content += '\n';

        if (meetingData.transcription.trim()) {
            content += '语音转录内容:\n' + meetingData.transcription + '\n\n';
        }

        if (meetingData.manualNotes.trim()) {
            content += '手动记录内容:\n' + meetingData.manualNotes + '\n';
        }

        return content;
    }

    // 获取不同模式的Prompt模板
    getPromptTemplate(mode) {
        const templates = {
            'ai-concise': `请将以下会议内容整理成简洁的会议纪要，要求：
1. 提取关键信息和重要决策
2. 使用简洁明了的语言
3. 按照逻辑顺序组织内容
4. 突出重点和结论`,

            'ai-detailed': `请将以下会议内容整理成详细的会议纪要，要求：
1. 完整记录讨论过程和要点
2. 详细描述各方观点和建议
3. 记录具体的数据和时间节点
4. 包含背景信息和上下文
5. 结构化呈现，便于查阅`,

            'ai-action': `请从以下会议内容中提取行动项和任务安排，要求：
1. 明确列出所有行动项
2. 标注负责人和截止时间
3. 按优先级排序
4. 包含具体的执行步骤
5. 标记依赖关系和风险点`
        };

        return templates[mode] || templates['ai-concise'];
    }

    // 创建AI增强的会议纪要
    createAIEnhancedSummary(meetingData, aiResponse, mode) {
        const formatTime = (timeString) => {
            if (!timeString) return '未设置';
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN');
        };

        const modeNames = {
            'ai-concise': 'AI简洁版',
            'ai-detailed': 'AI详细版',
            'ai-action': 'AI行动项提取'
        };

        return `
            <div class="summary-content ai-enhanced">
                <div class="ai-badge">
                    <i class="fas fa-robot"></i> ${modeNames[mode] || 'AI生成'}
                </div>

                <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #9b59b6; padding-bottom: 5px;">
                    📋 ${meetingData.title}
                </h4>

                <div style="margin-bottom: 20px;">
                    <p><strong>🕐 会议时间：</strong>${formatTime(meetingData.time)}</p>
                    <p><strong>👥 参会人员：</strong>${meetingData.participants || '未填写'}</p>
                    <p><strong>🤖 AI处理模式：</strong>${modeNames[mode] || mode}</p>
                </div>

                <div style="margin-bottom: 20px;">
                    <h5 style="color: #9b59b6; margin-bottom: 10px;">🤖 AI智能纪要</h5>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #9b59b6; white-space: pre-wrap; line-height: 1.8;">
                        ${aiResponse}
                    </div>
                </div>

                ${meetingData.transcription ? `
                <details style="margin-bottom: 15px;">
                    <summary style="cursor: pointer; color: #27ae60; font-weight: 500; margin-bottom: 10px;">
                        🎤 查看原始语音转录
                    </summary>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #27ae60; margin-top: 10px;">
                        ${meetingData.transcription.replace(/\n/g, '<br>')}
                    </div>
                </details>
                ` : ''}

                ${meetingData.manualNotes ? `
                <details style="margin-bottom: 15px;">
                    <summary style="cursor: pointer; color: #e74c3c; font-weight: 500; margin-bottom: 10px;">
                        ✏️ 查看手动记录
                    </summary>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #e74c3c; margin-top: 10px;">
                        ${meetingData.manualNotes.replace(/\n/g, '<br>')}
                    </div>
                </details>
                ` : ''}

                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 12px;">
                    📝 纪要生成时间：${new Date().toLocaleString('zh-CN')} |
                    🤖 AI模型：${this.aiConfig.model} |
                    🔧 服务商：${this.aiConfig.provider}
                </div>
            </div>
        `;
    }

    // 显示/隐藏AI处理状态
    showAIProcessingStatus(show) {
        const statusDiv = document.getElementById('aiProcessingStatus');
        const generateButton = document.getElementById('generateSummary');

        if (show) {
            statusDiv.style.display = 'block';
            generateButton.disabled = true;
            generateButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> AI处理中';

            // 更新处理消息
            const messages = [
                'AI正在分析会议内容...',
                'AI正在提取关键信息...',
                'AI正在生成结构化纪要...',
                'AI正在优化输出格式...'
            ];

            let messageIndex = 0;
            const messageInterval = setInterval(() => {
                if (statusDiv.style.display === 'none') {
                    clearInterval(messageInterval);
                    return;
                }

                document.getElementById('processingMessage').textContent = messages[messageIndex];
                messageIndex = (messageIndex + 1) % messages.length;
            }, 2000);

        } else {
            statusDiv.style.display = 'none';
            generateButton.disabled = false;
            generateButton.innerHTML = '<i class="fas fa-magic"></i> 生成纪要';
        }
    }

    // 设置音频源
    setAudioSource(source) {
        this.audioSource = source;
        console.log(`🔊 音频源已设置为: ${source}`);
        
        // 如果正在录音，需要重新启动
        if (this.isRecording) {
            this.stopRecording();
            setTimeout(() => this.startRecording(), 300);
        }
    }

    // 获取麦克风音频流
    async getMicrophoneStream() {
        console.log('🎤 获取麦克风音频...');
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                } 
            });
            console.log('✅ 麦克风音频获取成功');
            this.showToast('麦克风已连接', 'success');
            return stream;
        } catch (error) {
            console.error('❌ 麦克风访问失败:', error);
            this.showToast('无法访问麦克风，请检查浏览器权限设置', 'error');
            return null;
        }
    }

    // 获取系统音频流
    async getSystemAudioStream() {
        console.log('🔊 获取系统音频...');
        
        // 显示系统音频捕获指南
        this.showToast('请在弹出窗口中选择要共享的窗口并勾选"共享音频"选项', 'info');
        
        try {
            const stream = await navigator.mediaDevices.getDisplayMedia({ 
                video: {
                    cursor: 'never',
                    displaySurface: 'window'
                },
                audio: {
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false
                }
            });
            
            // 检查是否有音频轨道
            const audioTracks = stream.getAudioTracks();
            if (audioTracks.length === 0) {
                // 停止视频轨道
                stream.getVideoTracks().forEach(track => track.stop());
                throw new Error('未检测到系统音频，请确保您勾选了"共享音频"选项');
            }
            
            // 停止视频轨道，我们只需要音频
            stream.getVideoTracks().forEach(track => track.stop());
            
            console.log('✅ 系统音频获取成功');
            this.showToast('系统音频已连接', 'success');
            return stream;
        } catch (error) {
            console.error('❌ 系统音频获取失败:', error);
            this.showToast('系统音频获取失败: ' + error.message, 'error');
            return null;
        }
    }

    // 获取混合音频流（麦克风+系统音频）
    async getMixedAudioStream() {
        console.log('🔄 获取混合音频...');
        try {
            // 获取麦克风流
            const micStream = await this.getMicrophoneStream();
            if (!micStream) return null;
            
            // 获取系统音频流
            const sysStream = await this.getSystemAudioStream();
            if (!sysStream) {
                // 如果系统音频获取失败，至少返回麦克风音频
                return micStream;
            }
            
            // 使用Web Audio API混合两个音频流
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const micSource = audioContext.createMediaStreamSource(micStream);
            const sysSource = audioContext.createMediaStreamSource(sysStream);
            const destination = audioContext.createMediaStreamDestination();
            
            micSource.connect(destination);
            sysSource.connect(destination);
            
            console.log('✅ 混合音频创建成功');
            this.showToast('混合音频已连接', 'success');
            
            // 保存引用以便后续清理
            this.audioContext = audioContext;
            this.micStream = micStream;
            this.sysStream = sysStream;
            
            return destination.stream;
        } catch (error) {
            console.error('❌ 混合音频创建失败:', error);
            this.showToast('混合音频创建失败: ' + error.message, 'error');
            return null;
        }
    }

    // 启动语音识别
    async startSpeechRecognition() {
        try {
            console.log('🚀 初始化语音识别...');
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();

            // 配置语音识别 - 修复关键参数
            this.recognition.continuous = true;        // 持续识别
            this.recognition.interimResults = true;    // 显示中间结果
            this.recognition.lang = 'zh-CN';           // 中文识别
            this.recognition.maxAlternatives = 1;

            console.log('⚙️ 语音识别配置完成:', {
                continuous: this.recognition.continuous,
                interimResults: this.recognition.interimResults,
                lang: this.recognition.lang
            });

            // 设置事件处理器
            this.recognition.onstart = () => {
                console.log('✅ 语音识别已开始');
                this.isRecording = true;
                this.isPaused = false;
                
                // 显示初始状态消息
                const transcriptionDiv = document.getElementById('transcriptionText');
                if (transcriptionDiv && transcriptionDiv.innerHTML.includes('placeholder-text')) {
                    transcriptionDiv.innerHTML = '<p>语音识别已启动，请开始说话...</p>';
                }
            };

            this.recognition.onresult = (event) => {
                let interimTranscript = '';
                let finalTranscript = '';

                // 处理识别结果
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    
                    if (event.results[i].isFinal) {
                        finalTranscript += transcript + ' ';
                        console.log('✅ 最终识别结果:', transcript);
                    } else {
                        interimTranscript += transcript;
                        console.log('🔄 临时识别结果:', transcript);
                    }
                }

                // 更新转录显示 - 修复变量名错误
                this.updateTranscriptionDisplay(finalTranscript, interimTranscript);
            };

            this.recognition.onerror = (event) => {
                console.error('❌ 语音识别错误:', event.error);
                
                // 对常见错误提供友好提示
                let errorMessage = '语音识别出现错误';
                if (event.error === 'no-speech') {
                    errorMessage = '未检测到语音，请确保麦克风工作正常';
                } else if (event.error === 'audio-capture') {
                    errorMessage = '无法捕获音频，请检查麦克风设备';
                } else if (event.error === 'not-allowed') {
                    errorMessage = '麦克风访问被拒绝，请检查浏览器权限设置';
                } else if (event.error === 'network') {
                    errorMessage = '网络错误，请检查您的网络连接';
                }
                
                this.showToast(errorMessage, 'warning');
            };

            this.recognition.onend = () => {
                console.log('🔄 语音识别会话结束');
                
                // 如果仍在录音状态，自动重启识别
                if (this.isRecording && !this.isPaused) {
                    console.log('🔄 自动重启语音识别...');
                    setTimeout(() => {
                        if (this.isRecording) {
                            this.recognition.start();
                        }
                    }, 100);
                }
            };

            console.log('🎯 启动语音识别...');
            this.recognition.start();
            this.showToast('语音识别已启动', 'success');

        } catch (error) {
            console.error('❌ 启动语音识别失败:', error);
            this.showToast('启动语音识别失败: ' + error.message, 'error');
            this.updateRecordingUI(false);
        }
    }

    // 显示音频设置帮助
    showAudioHelp() {
        this.showModal(`
            <h3>音频设置帮助</h3>
            
            <h4>麦克风输入</h4>
            <p>直接使用您的麦克风捕获声音，适合：</p>
            <ul>
                <li>个人语音记录</li>
                <li>面对面会议记录</li>
            </ul>
            
            <h4>系统音频</h4>
            <p>捕获计算机播放的声音，适合：</p>
            <ul>
                <li>腾讯会议、Zoom等视频会议</li>
                <li>在线课程、网络讲座</li>
            </ul>
            <p><strong>使用方法：</strong></p>
            <ol>
                <li>选择"系统音频"</li>
                <li>点击"开始录音"</li>
                <li>在弹出窗口中选择要共享的窗口/标签</li>
                <li><strong>重要：</strong> 勾选"共享音频"选项</li>
            </ol>
            
            <h4>混合音频</h4>
            <p>同时捕获麦克风和系统音频，适合：</p>
            <ul>
                <li>需要记录自己和他人发言的会议</li>
                <li>在线访谈、远程教学</li>
            </ul>
            
            <h4>浏览器兼容性</h4>
            <p>系统音频捕获功能需要现代浏览器支持：</p>
            <ul>
                <li>Chrome 74+</li>
                <li>Edge 79+</li>
                <li>Safari 13+ (部分支持)</li>
            </ul>
            <p>Firefox目前不支持系统音频捕获。</p>
        `, false);
    }

    // 创建音频可视化器
    createAudioVisualizer(stream) {
        // 获取可视化容器
        const visualizer = document.getElementById('audioVisualizer');
        if (!visualizer) return;
        
        // 清除现有内容
        visualizer.innerHTML = '';
        visualizer.style.display = 'block';
        
        // 创建音频上下文
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const analyser = audioContext.createAnalyser();
        const source = audioContext.createMediaStreamSource(stream);
        
        source.connect(analyser);
        analyser.fftSize = 256;
        
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        
        // 创建可视化元素
        for (let i = 0; i < bufferLength; i++) {
            const bar = document.createElement('div');
            bar.className = 'visualizer-bar';
            visualizer.appendChild(bar);
        }
        
        const bars = visualizer.querySelectorAll('.visualizer-bar');
        
        // 更新可视化
        const updateVisualizer = () => {
            if (!this.isRecording) {
                visualizer.style.display = 'none';
                return;
            }
            
            requestAnimationFrame(updateVisualizer);
            analyser.getByteFrequencyData(dataArray);
            
            for (let i = 0; i < bars.length; i++) {
                const barHeight = dataArray[i] / 2;
                bars[i].style.height = barHeight + 'px';
                
                // 根据音量设置颜色
                const hue = (barHeight / 128) * 120; // 从红色到绿色
                bars[i].style.backgroundColor = `hsl(${hue}, 100%, 50%)`;
            }
        };
        
        updateVisualizer();
        
        // 保存引用以便后续清理
        this.visualizerContext = audioContext;
    }

    // 处理笔记获得焦点
    handleNotesFocus() {
        // 如果笔记为空，自动添加时间戳
        const textarea = document.getElementById('manualNotes');
        if (textarea && textarea.value.trim() === '') {
            this.addTimestamp();
        }
    }

    // 处理笔记输入
    handleNotesInput() {
        const textarea = document.getElementById('manualNotes');
        if (!textarea) return;
        
        // 获取最后一行
        const lines = textarea.value.split('\n');
        const lastLine = lines[lines.length - 1];
        
        // 如果最后一行是空行且不是第一行，自动添加时间戳
        if (lastLine.trim() === '' && lines.length > 1 && lines[lines.length - 2].trim() !== '') {
            // 删除空行
            lines.pop();
            // 添加带时间戳的新行
            lines.push('');
            lines.push(this.getTimestampString() + ' ');
            
            // 更新文本区域
            textarea.value = lines.join('\n');
            
            // 将光标移到最后
            textarea.focus();
            textarea.selectionStart = textarea.value.length;
            textarea.selectionEnd = textarea.value.length;
        }
    }

    // 添加时间戳
    addTimestamp() {
        const textarea = document.getElementById('manualNotes');
        if (!textarea) return;
        
        const timestamp = this.getTimestampString();
        const cursorPos = textarea.selectionStart;
        const textBefore = textarea.value.substring(0, cursorPos);
        const textAfter = textarea.value.substring(cursorPos);
        
        // 检查是否需要添加换行
        const needsNewline = textBefore.length > 0 && 
                             !textBefore.endsWith('\n') && 
                             !textBefore.endsWith('\r\n');
        
        const insertion = (needsNewline ? '\n' : '') + timestamp + ' ';
        textarea.value = textBefore + insertion + textAfter;
        
        // 将光标移到时间戳后
        const newPosition = cursorPos + insertion.length;
        textarea.focus();
        textarea.selectionStart = newPosition;
        textarea.selectionEnd = newPosition;
        
        // 显示状态消息
        this.updateNoteStatus('已插入时间戳');
    }

    // 获取格式化的时间戳字符串
    getTimestampString() {
        const now = new Date();
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        
        return `[${hours}:${minutes}:${seconds}]`;
    }

    // 清空手动笔记
    clearManualNotes() {
        const textarea = document.getElementById('manualNotes');
        if (textarea) {
            if (textarea.value.trim() !== '' && !confirm('确定要清空所有笔记内容吗？')) {
                return;
            }
            
            textarea.value = '';
            this.updateNoteStatus('笔记已清空');
            
            // 自动添加时间戳
            setTimeout(() => this.addTimestamp(), 100);
        }
    }

    // 更新笔记状态
    updateNoteStatus(message) {
        const status = document.getElementById('noteStatus');
        if (status) {
            status.textContent = message;
            status.style.opacity = 1;
            
            // 3秒后淡出
            setTimeout(() => {
                status.style.opacity = 0;
            }, 3000);
        }
    }

    // 生成会议纪要时整合时间戳对齐的内容
    generateSummary() {
        const meetingTitle = document.getElementById('meetingTitle').value || '未命名会议';
        const meetingTime = document.getElementById('meetingTime').value;
        const participants = document.getElementById('participants').value;
        const transcription = this.transcriptionText;
        const manualNotes = document.getElementById('manualNotes').value;
        const summaryMode = document.getElementById('summaryMode').value;

        if (!transcription.trim() && !manualNotes.trim()) {
            this.updateStatusIndicator('请先录制语音或输入手动笔记后再生成纪要', 'warning');
            return;
        }

        // 显示处理状态
        const aiProcessingStatus = document.getElementById('aiProcessingStatus');
        if (aiProcessingStatus) {
            aiProcessingStatus.style.display = 'block';
        }
        
        // 更新进度条
        this.updateProgressBar(0);
        
        // 准备会议数据
        const meetingData = {
            title: meetingTitle,
            time: meetingTime,
            participants: participants,
            transcription: transcription,
            manualNotes: manualNotes
        };

        // 根据模式生成纪要
        let summaryPromise;
        
        if (summaryMode === 'basic') {
            // 基础整理模式
            summaryPromise = this.generateBasicSummary(meetingData);
        } else {
            // AI模式
            summaryPromise = this.generateAISummary(meetingData, summaryMode);
        }
        
        // 处理生成结果
        summaryPromise.then(summary => {
            document.getElementById('meetingSummary').innerHTML = summary;
            
            // 隐藏处理状态
            if (aiProcessingStatus) {
                aiProcessingStatus.style.display = 'none';
            }
            
            this.updateStatusIndicator('会议纪要已生成', 'success');
        }).catch(error => {
            console.error('生成纪要失败:', error);
            
            // 隐藏处理状态
            if (aiProcessingStatus) {
                aiProcessingStatus.style.display = 'none';
            }
            
            this.updateStatusIndicator('生成纪要失败: ' + error.message, 'error');
        });
    }

    // 基础整理模式
    async generateBasicSummary(meetingData) {
        // 模拟进度
        for (let i = 0; i <= 100; i += 10) {
            this.updateProgressBar(i);
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // 整合转录内容和手动笔记
        let summary = `<h3>${meetingData.title || '会议纪要'}</h3>`;
        
        if (meetingData.time) {
            const date = new Date(meetingData.time);
            summary += `<p><strong>时间：</strong>${date.toLocaleString('zh-CN')}</p>`;
        }
        
        if (meetingData.participants) {
            summary += `<p><strong>参会人员：</strong>${meetingData.participants}</p>`;
        }
        
        summary += '<hr>';
        
        // 提取并对齐时间戳
        const alignedContent = this.alignTimeStampedContent(meetingData.transcription, meetingData.manualNotes);
        
        summary += '<h4>会议内容：</h4>';
        summary += `<div class="summary-content">${alignedContent}</div>`;
        
        return summary;
    }

    // 对齐时间戳内容
    alignTimeStampedContent(transcription, notes) {
        // 解析转录内容中的时间戳
        const transcriptLines = transcription.split('\n');
        const transcriptMap = new Map();
        
        // 假设转录内容中可能有时间戳
        for (const line of transcriptLines) {
            const timestampMatch = line.match(/\[(\d{2}:\d{2}(?::\d{2})?)\]/);
            if (timestampMatch) {
                const timestamp = timestampMatch[1];
                const content = line.replace(timestampMatch[0], '').trim();
                transcriptMap.set(timestamp, content);
            }
        }
        
        // 解析笔记中的时间戳
        const noteLines = notes.split('\n');
        const noteMap = new Map();
        let currentTimestamp = null;
        let currentContent = [];
        
        for (const line of noteLines) {
            const timestampMatch = line.match(/\[(\d{2}:\d{2}(?::\d{2})?)\]/);
            if (timestampMatch) {
                // 如果已有时间戳，保存之前的内容
                if (currentTimestamp) {
                    noteMap.set(currentTimestamp, currentContent.join('\n'));
                }
                
                // 设置新的时间戳和内容
                currentTimestamp = timestampMatch[1];
                currentContent = [line.replace(timestampMatch[0], '').trim()];
            } else if (currentTimestamp && line.trim()) {
                // 添加到当前时间戳的内容
                currentContent.push(line.trim());
            }
        }
        
        // 保存最后一个时间戳的内容
        if (currentTimestamp) {
            noteMap.set(currentTimestamp, currentContent.join('\n'));
        }
        
        // 合并并按时间排序
        const allTimestamps = [...new Set([...transcriptMap.keys(), ...noteMap.keys()])];
        allTimestamps.sort(); // 按时间戳排序
        
        // 构建对齐的内容
        let alignedContent = '';
        
        for (const timestamp of allTimestamps) {
            alignedContent += `<div class="timestamp-section">`;
            alignedContent += `<div class="timestamp">${timestamp}</div>`;
            
            // 添加转录内容
            if (transcriptMap.has(timestamp)) {
                alignedContent += `<div class="transcript-content">${transcriptMap.get(timestamp)}</div>`;
            }
            
            // 添加笔记内容（使用不同样式突出显示）
            if (noteMap.has(timestamp)) {
                alignedContent += `<div class="note-content"><i class="fas fa-star"></i> ${noteMap.get(timestamp)}</div>`;
            }
            
            alignedContent += `</div>`;
        }
        
        // 如果没有时间戳内容，则直接显示原始内容
        if (allTimestamps.length === 0) {
            if (transcription.trim()) {
                alignedContent += `<div class="transcript-content">${transcription}</div>`;
            }
            
            if (notes.trim()) {
                alignedContent += `<div class="note-content">${notes}</div>`;
            }
        }
        
        return alignedContent;
    }

    // 更新进度条
    updateProgressBar(percent) {
        const progressFill = document.getElementById('progressFill');
        if (progressFill) {
            progressFill.style.width = `${percent}%`;
        }
    }
}

    // 创建会议纪要HTML
    createMeetingSummary(data) {
        const formatTime = (timeString) => {
            if (!timeString) return '未设置';
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN');
        };
        
        return `
            <div class="summary-content">
                <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px;">
                    📋 ${data.title}
                </h4>
                
                <div style="margin-bottom: 20px;">
                    <p><strong>🕐 会议时间：</strong>${formatTime(data.time)}</p>
                    <p><strong>👥 参会人员：</strong>${data.participants || '未填写'}</p>
                </div>
                
                ${data.transcription ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #27ae60; margin-bottom: 10px;">🎤 语音转录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #27ae60;">
                        ${data.transcription.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}
                
                ${data.manualNotes ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #e74c3c; margin-bottom: 10px;">✏️ 手动记录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #e74c3c;">
                        ${data.manualNotes.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}
                
                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 12px;">
                    📝 纪要生成时间：${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
    }

    // 保存会议记录
    saveMeeting() {
        const meetingData = {
            id: Date.now(),
            title: document.getElementById('meetingTitle').value || '未命名会议',
            time: document.getElementById('meetingTime').value,
            participants: document.getElementById('participants').value,
            transcription: this.transcriptionText,
            manualNotes: document.getElementById('manualNotes').value,
            summary: document.getElementById('meetingSummary').innerHTML,
            savedAt: new Date().toISOString()
        };
        
        if (!meetingData.transcription.trim() && !meetingData.manualNotes.trim()) {
            this.showModal('没有内容可保存，请先录制语音或输入笔记。');
            return;
        }
        
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        savedMeetings.unshift(meetingData);
        
        // 只保留最近50条记录
        if (savedMeetings.length > 50) {
            savedMeetings.splice(50);
        }
        
        localStorage.setItem('meetingNotes', JSON.stringify(savedMeetings));
        this.showModal('会议记录已保存成功！');
        this.loadSavedMeetings();
    }

    // 导出会议记录
    exportMeeting() {
        const summary = document.getElementById('meetingSummary').innerHTML;
        if (!summary || summary.includes('placeholder-text')) {
            this.showModal('请先生成会议纪要后再导出。');
            return;
        }
        
        const meetingTitle = document.getElementById('meetingTitle').value || '会议记录';
        const exportContent = this.createExportContent();
        
        const blob = new Blob([exportContent], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${meetingTitle}_${new Date().toISOString().slice(0, 10)}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showModal('会议记录已导出成功！');
    }

    // 创建导出内容
    createExportContent() {
        const summary = document.getElementById('meetingSummary').innerHTML;
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议记录 - Jason's Meeting Note</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; line-height: 1.6; margin: 40px; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { max-width: 800px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📝 Jason's Meeting Note</h1>
        <p>智能会议记录系统导出文档</p>
    </div>
    <div class="content">
        ${summary}
    </div>
</body>
</html>
        `;
    }

    // 新建会议
    newMeeting() {
        if (confirm('确定要新建会议吗？当前未保存的内容将会丢失。')) {
            document.getElementById('meetingTitle').value = '';
            document.getElementById('participants').value = '';
            this.clearTranscription();
            this.clearManualNotes();
            document.getElementById('meetingSummary').innerHTML = '<p class="placeholder-text">点击"生成纪要"按钮，系统将自动整合语音转录和手动笔记，生成结构化的会议纪要...</p>';
            this.setDefaultDateTime();
            this.stopRecording();
        }
    }

    // 加载保存的会议
    loadSavedMeetings() {
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        const historyList = document.getElementById('historyList');
        
        if (savedMeetings.length === 0) {
            historyList.innerHTML = '<p class="placeholder-text">暂无历史会议记录</p>';
            return;
        }
        
        historyList.innerHTML = savedMeetings.map(meeting => `
            <div class="history-item" style="border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 10px; cursor: pointer;" 
                 onclick="meetingApp.loadMeetingData(${meeting.id})">
                <h5 style="margin: 0 0 5px 0; color: #2c3e50;">${meeting.title}</h5>
                <p style="margin: 0; color: #6c757d; font-size: 12px;">
                    保存时间: ${new Date(meeting.savedAt).toLocaleString('zh-CN')}
                </p>
            </div>
        `).join('');
    }

    // 加载特定会议数据
    loadMeetingData(meetingId) {
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        const meeting = savedMeetings.find(m => m.id === meetingId);
        
        if (meeting) {
            document.getElementById('meetingTitle').value = meeting.title;
            document.getElementById('meetingTime').value = meeting.time;
            document.getElementById('participants').value = meeting.participants;
            this.transcriptionText = meeting.transcription;
            document.getElementById('manualNotes').value = meeting.manualNotes;
            
            this.updateTranscriptionDisplay(meeting.transcription, '');
            if (meeting.summary) {
                document.getElementById('meetingSummary').innerHTML = meeting.summary;
            }
            
            this.toggleHistorySection();
            this.showModal('会议记录已加载成功！');
        }
    }

    // 切换历史记录显示
    toggleHistorySection() {
        const historySection = document.querySelector('.history-section');
        if (historySection.style.display === 'none' || !historySection.style.display) {
            historySection.style.display = 'block';
            this.loadSavedMeetings();
        } else {
            historySection.style.display = 'none';
        }
    }

    // 显示模态框
    showModal(message) {
        document.getElementById('modalMessage').innerHTML = message;
        document.getElementById('statusModal').style.display = 'block';
    }

    // 关闭模态框
    closeModal() {
        document.getElementById('statusModal').style.display = 'none';
    }

    // ==================== 调试和验证方法 ====================

    // 快速验证所有关键元素
    validateAllElements() {
        console.log('🔍 快速验证所有关键元素...');

        const criticalElements = [
            // AI设置相关
            'toggleAISettings', 'aiSettingsPanel', 'aiProvider', 'apiKey', 'apiBaseUrl',
            'modelName', 'temperature', 'temperatureValue', 'maxTokens', 'customPrompt',
            'toggleApiKey', 'testConnection', 'saveAIConfig', 'clearAIConfig',
            'connectionStatus', 'statusIndicator', 'statusMessage', 'apiConfigSection',

            // 语音识别相关
            'startRecording', 'stopRecording', 'pauseRecording', 'recordingStatus',
            'transcriptionText', 'clearTranscription',

            // 手动输入相关
            'manualNotes', 'clearManualNotes', 'addTimestamp',

            // 会议信息相关
            'meetingTitle', 'meetingTime', 'participants',

            // 纪要生成相关
            'summaryMode', 'generateSummary', 'meetingSummary',
            'aiProcessingStatus', 'processingMessage', 'progressFill',

            // 操作按钮相关
            'saveMeeting', 'exportMeeting', 'newMeeting', 'loadMeeting',

            // 其他界面元素
            'statusModal', 'modalMessage', 'historyList'
        ];

        let foundCount = 0;
        let missingElements = [];

        criticalElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                foundCount++;
                console.log(`✅ ${elementId}: 找到`);
            } else {
                missingElements.push(elementId);
                console.error(`❌ ${elementId}: 未找到`);
            }
        });

        const completeness = Math.round((foundCount / criticalElements.length) * 100);

        console.log(`\n📊 元素验证结果:`);
        console.log(`总元素数: ${criticalElements.length}`);
        console.log(`找到元素: ${foundCount}`);
        console.log(`缺失元素: ${missingElements.length}`);
        console.log(`完整性: ${completeness}%`);

        if (missingElements.length > 0) {
            console.log(`\n❌ 缺失的元素:`);
            missingElements.forEach(id => console.log(`  • ${id}`));

            // 显示用户友好的提示
            this.showModal(`发现 ${missingElements.length} 个缺失的界面元素，这可能影响功能正常使用。\n\n缺失元素: ${missingElements.slice(0, 5).join(', ')}${missingElements.length > 5 ? '...' : ''}\n\n请检查控制台获取详细信息。`);
        } else {
            console.log(`\n🎉 所有关键元素都已找到！应用应该能正常工作。`);
        }

        return {
            total: criticalElements.length,
            found: foundCount,
            missing: missingElements,
            completeness: completeness
        };
    }

    // 显示调试面板
    showDebugPanel() {
        const debugPanel = document.getElementById('debugPanel');
        if (debugPanel) {
            debugPanel.style.display = 'block';
            this.updateDebugInfo();
        }
    }

    // 隐藏调试面板
    hideDebugPanel() {
        const debugPanel = document.getElementById('debugPanel');
        if (debugPanel) {
            debugPanel.style.display = 'none';
        }
    }

    // 更新调试信息
    updateDebugInfo() {
        const elementStatus = document.getElementById('debugElementStatus');
        const appStatus = document.getElementById('debugAppStatus');

        if (elementStatus) {
            const validation = this.validateAllElements();
            elementStatus.textContent = `元素完整性: ${validation.completeness}%\n找到: ${validation.found}/${validation.total}\n缺失: ${validation.missing.length}`;
        }

        if (appStatus) {
            appStatus.textContent = `录音状态: ${this.isRecording ? '录音中' : '未录音'}\n暂停状态: ${this.isPaused ? '已暂停' : '未暂停'}\nAI配置: ${this.aiConfig ? '已配置' : '未配置'}\n初始化: ${window.meetingApp ? '成功' : '失败'}`;
        }
    }

    // ==================== AI配置管理方法 ====================

    // 切换AI设置面板显示
    toggleAISettings(show) {
        const panel = document.getElementById('aiSettingsPanel');
        const button = document.getElementById('toggleAISettings');
        
        if (!panel || !button) return;
        
        // 如果show参数未定义，则切换显示状态
        const newState = (show !== undefined) ? show : (panel.style.display === 'none' || !panel.style.display);
        
        panel.style.display = newState ? 'block' : 'none';
        button.innerHTML = newState ? 
            '<i class="fas fa-chevron-up"></i> 收起设置' : 
            '<i class="fas fa-cog"></i> 配置AI';
    }

    // 加载AI配置
    loadAIConfig() {
        const savedConfig = localStorage.getItem('aiConfig');
        if (savedConfig) {
            try {
                this.aiConfig = JSON.parse(savedConfig);
                this.populateAIConfigForm();
            } catch (error) {
                console.error('加载AI配置失败:', error);
                this.aiConfig = null;
            }
        }
    }

    // 填充AI配置表单
    populateAIConfigForm() {
        if (!this.aiConfig) return;

        document.getElementById('aiProvider').value = this.aiConfig.provider || '';
        document.getElementById('apiKey').value = this.aiConfig.apiKey || '';
        document.getElementById('apiBaseUrl').value = this.aiConfig.baseUrl || '';
        document.getElementById('modelName').value = this.aiConfig.model || '';
        document.getElementById('temperature').value = this.aiConfig.temperature || 0.7;
        document.getElementById('maxTokens').value = this.aiConfig.maxTokens || 2000;
        document.getElementById('customPrompt').value = this.aiConfig.customPrompt || '';

        this.updateTemperatureDisplay(this.aiConfig.temperature || 0.7);

        if (this.aiConfig.provider) {
            this.onProviderChange(this.aiConfig.provider);
        }
    }

    // AI服务商变更处理
    onProviderChange(provider) {
        const configSection = document.getElementById('apiConfigSection');
        const baseUrlInput = document.getElementById('apiBaseUrl');
        const modelSelect = document.getElementById('modelName');

        if (provider) {
            configSection.style.display = 'block';

            // 设置默认Base URL和可用模型
            const providerConfigs = this.getProviderConfigs();
            const config = providerConfigs[provider];

            if (config) {
                baseUrlInput.value = config.baseUrl;
                baseUrlInput.placeholder = config.baseUrl;

                // 更新模型选项
                modelSelect.innerHTML = '<option value="">请选择模型</option>';
                config.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.value;
                    option.textContent = model.name;
                    modelSelect.appendChild(option);
                });
            }
        } else {
            configSection.style.display = 'none';
        }
    }

    // 获取各服务商的配置信息
    getProviderConfigs() {
        return {
            deepseek: {
                baseUrl: 'https://api.deepseek.com/v1',
                models: [
                    { value: 'deepseek-chat', name: 'DeepSeek Chat' },
                    { value: 'deepseek-coder', name: 'DeepSeek Coder' }
                ]
            },
            minimax: {
                baseUrl: 'https://api.minimax.chat/v1',
                models: [
                    { value: 'abab6.5s-chat', name: 'ABAB 6.5s Chat' },
                    { value: 'abab6.5-chat', name: 'ABAB 6.5 Chat' }
                ]
            },
            doubao: {
                baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
                models: [
                    { value: 'ep-20241218143820-lmvzr', name: '豆包 Pro' },
                    { value: 'ep-20241218143820-lmvzs', name: '豆包 Lite' }
                ]
            },
            gemini: {
                baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
                models: [
                    { value: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
                    { value: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' }
                ]
            },
            openai: {
                baseUrl: 'https://api.openai.com/v1',
                models: [
                    { value: 'gpt-4o', name: 'GPT-4o' },
                    { value: 'gpt-4o-mini', name: 'GPT-4o Mini' },
                    { value: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
                ]
            },
            custom: {
                baseUrl: '',
                models: [
                    { value: 'custom-model', name: '自定义模型' }
                ]
            }
        };
    }

    // 切换API Key显示/隐藏
    toggleApiKeyVisibility() {
        const apiKeyInput = document.getElementById('apiKey');
        const toggleButton = document.getElementById('toggleApiKey');

        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            toggleButton.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            apiKeyInput.type = 'password';
            toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
        }
    }

    // 更新温度显示
    updateTemperatureDisplay(value) {
        document.getElementById('temperatureValue').textContent = value;
    }

    // 测试API连接
    async testAPIConnection() {
        const provider = document.getElementById('aiProvider').value;
        const apiKey = document.getElementById('apiKey').value;
        const baseUrl = document.getElementById('apiBaseUrl').value;
        const model = document.getElementById('modelName').value;

        if (!provider || !apiKey || !baseUrl || !model) {
            this.showModal('请先完整填写AI配置信息');
            return;
        }

        const statusIndicator = document.getElementById('statusIndicator');
        const statusMessage = document.getElementById('statusMessage');
        const testButton = document.getElementById('testConnection');

        // 更新UI状态
        statusIndicator.className = 'status-indicator testing';
        statusIndicator.textContent = '测试中';
        statusMessage.textContent = '正在连接API...';
        testButton.disabled = true;
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中';

        try {
            const testResult = await this.performAPITest(provider, apiKey, baseUrl, model);

            if (testResult.success) {
                statusIndicator.className = 'status-indicator connected';
                statusIndicator.textContent = '连接成功';
                statusMessage.textContent = testResult.message || '连接正常，可以使用AI功能';
            } else {
                statusIndicator.className = 'status-indicator failed';
                statusIndicator.textContent = '连接失败';
                statusMessage.textContent = testResult.error || '连接失败，请检查配置';
            }
        } catch (error) {
            statusIndicator.className = 'status-indicator failed';
            statusIndicator.textContent = '连接失败';
            statusMessage.textContent = `错误: ${error.message}`;
        } finally {
            testButton.disabled = false;
            testButton.innerHTML = '<i class="fas fa-plug"></i> 测试连接';
        }
    }

    // 执行API测试
    async performAPITest(provider, apiKey, baseUrl, model) {
        const testMessage = "Hello, this is a connection test.";

        try {
            const response = await this.callAIAPI(provider, apiKey, baseUrl, model, testMessage, {
                temperature: 0.1,
                max_tokens: 50
            });

            return {
                success: true,
                message: `连接成功，模型响应正常 (${response.length} 字符)`
            };
        } catch (error) {
            return {
                success: false,
                error: this.parseAPIError(error)
            };
        }
    }

    // 解析API错误
    parseAPIError(error) {
        if (error.message.includes('401')) {
            return 'API Key无效或已过期';
        } else if (error.message.includes('403')) {
            return 'API访问被拒绝，请检查权限';
        } else if (error.message.includes('429')) {
            return 'API调用频率超限，请稍后重试';
        } else if (error.message.includes('500')) {
            return 'API服务器内部错误';
        } else if (error.message.includes('timeout')) {
            return '连接超时，请检查网络';
        } else if (error.message.includes('Failed to fetch')) {
            return '网络连接失败，请检查网络或API地址';
        } else {
            return error.message || '未知错误';
        }
    }

    // 保存AI配置
    saveAIConfig() {
        const config = {
            provider: document.getElementById('aiProvider').value,
            apiKey: document.getElementById('apiKey').value,
            baseUrl: document.getElementById('apiBaseUrl').value,
            model: document.getElementById('modelName').value,
            temperature: parseFloat(document.getElementById('temperature').value),
            maxTokens: parseInt(document.getElementById('maxTokens').value),
            customPrompt: document.getElementById('customPrompt').value,
            savedAt: new Date().toISOString()
        };

        if (!config.provider || !config.apiKey || !config.model) {
            this.showModal('请先完整填写必要的配置信息');
            return;
        }

        try {
            localStorage.setItem('aiConfig', JSON.stringify(config));
            this.aiConfig = config;
            this.showModal('AI配置已保存成功！');
        } catch (error) {
            console.error('保存AI配置失败:', error);
            this.showModal('保存配置失败，请重试');
        }
    }

    // 清空AI配置
    clearAIConfig() {
        if (confirm('确定要清空所有AI配置吗？此操作不可撤销。')) {
            localStorage.removeItem('aiConfig');
            this.aiConfig = null;

            // 重置表单
            document.getElementById('aiProvider').value = '';
            document.getElementById('apiKey').value = '';
            document.getElementById('apiBaseUrl').value = '';
            document.getElementById('modelName').innerHTML = '<option value="">请先选择AI服务商</option>';
            document.getElementById('temperature').value = 0.7;
            document.getElementById('maxTokens').value = 2000;
            document.getElementById('customPrompt').value = '';

            // 隐藏配置区域
            document.getElementById('apiConfigSection').style.display = 'none';

            // 重置连接状态
            const statusIndicator = document.getElementById('statusIndicator');
            const statusMessage = document.getElementById('statusMessage');
            statusIndicator.className = 'status-indicator';
            statusIndicator.textContent = '未测试';
            statusMessage.textContent = '';

            this.updateTemperatureDisplay(0.7);
            this.showModal('AI配置已清空');
        }
    }

    // ==================== AI API调用核心方法 ====================

    // 调用AI API的通用方法
    async callAIAPI(provider, apiKey, baseUrl, model, content, options = {}) {
        const timeout = options.timeout || 30000; // 30秒超时

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        try {
            let requestConfig;

            switch (provider) {
                case 'openai':
                case 'deepseek':
                case 'custom':
                    requestConfig = this.buildOpenAIRequest(apiKey, baseUrl, model, content, options);
                    break;
                case 'gemini':
                    requestConfig = this.buildGeminiRequest(apiKey, baseUrl, model, content, options);
                    break;
                case 'minimax':
                    requestConfig = this.buildMinimaxRequest(apiKey, baseUrl, model, content, options);
                    break;
                case 'doubao':
                    requestConfig = this.buildDoubaoRequest(apiKey, baseUrl, model, content, options);
                    break;
                default:
                    throw new Error(`不支持的AI服务商: ${provider}`);
            }

            const response = await fetch(requestConfig.url, {
                ...requestConfig.options,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return this.extractResponseContent(provider, data);

        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请检查网络连接');
            }
            throw error;
        }
    }

    // 构建OpenAI格式的请求
    buildOpenAIRequest(apiKey, baseUrl, model, content, options) {
        return {
            url: `${baseUrl}/chat/completions`,
            options: {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: model,
                    messages: [
                        {
                            role: 'user',
                            content: content
                        }
                    ],
                    temperature: options.temperature || 0.7,
                    max_tokens: options.max_tokens || 2000
                })
            }
        };
    }

    // 构建Gemini格式的请求
    buildGeminiRequest(apiKey, baseUrl, model, content, options) {
        return {
            url: `${baseUrl}/models/${model}:generateContent?key=${apiKey}`,
            options: {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: content
                        }]
                    }],
                    generationConfig: {
                        temperature: options.temperature || 0.7,
                        maxOutputTokens: options.max_tokens || 2000
                    }
                })
            }
        };
    }

    // 构建MiniMax格式的请求
    buildMinimaxRequest(apiKey, baseUrl, model, content, options) {
        return {
            url: `${baseUrl}/chat/completions`,
            options: {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: model,
                    messages: [
                        {
                            role: 'user',
                            content: content
                        }
                    ],
                    temperature: options.temperature || 0.7,
                    max_tokens: options.max_tokens || 2000
                })
            }
        };
    }

    // 构建豆包格式的请求
    buildDoubaoRequest(apiKey, baseUrl, model, content, options) {
        return {
            url: `${baseUrl}/chat/completions`,
            options: {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: model,
                    messages: [
                        {
                            role: 'user',
                            content: content
                        }
                    ],
                    temperature: options.temperature || 0.7,
                    max_tokens: options.max_tokens || 2000
                })
            }
        };
    }

    // 提取响应内容
    extractResponseContent(provider, data) {
        try {
            switch (provider) {
                case 'openai':
                case 'deepseek':
                case 'minimax':
                case 'doubao':
                case 'custom':
                    return data.choices[0].message.content;
                case 'gemini':
                    return data.candidates[0].content.parts[0].text;
                default:
                    throw new Error(`不支持的响应格式: ${provider}`);
            }
        } catch (error) {
            console.error('解析AI响应失败:', error, data);
            throw new Error('AI响应格式错误，请检查API配置');
        }
    }
}

// 初始化应用
let meetingApp;
window.meetingApp = null;

document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Jason\'s Meeting Note 正在初始化...');
    try {
        meetingApp = new MeetingNoteApp();
        window.meetingApp = meetingApp;
        console.log('✅ 应用初始化成功');
    } catch (error) {
        console.error('❌ 应用初始化失败:', error);
        alert('应用初始化失败，请刷新页面重试。错误信息: ' + error.message);
    }
});

// 在文件末尾添加以下代码，确保DOM完全加载后重新绑定事件
document.addEventListener('DOMContentLoaded', () => {
    // 检查应用是否已初始化
    if (!window.meetingApp) {
        console.error('❌ 应用未初始化，尝试重新初始化...');
        try {
            window.meetingApp = new MeetingNoteApp();
            console.log('✅ 应用重新初始化成功');
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
        }
    } else {
        // 重新绑定关键事件
        console.log('🔄 重新绑定事件监听器...');
        const app = window.meetingApp;
        
        // 重新绑定AI设置按钮
        app.addEventListenerSafely('toggleAISettings', 'click', () => app.toggleAISettings());
        
        // 重新绑定录音按钮
        app.addEventListenerSafely('startRecording', 'click', () => app.startRecording());
        app.addEventListenerSafely('stopRecording', 'click', () => app.stopRecording());
        app.addEventListenerSafely('pauseRecording', 'click', () => app.pauseRecording());
        
        // 重新绑定生成纪要按钮
        app.addEventListenerSafely('generateSummary', 'click', () => app.generateSummary());
        
        console.log('✅ 事件监听器重新绑定完成');
    }
});
