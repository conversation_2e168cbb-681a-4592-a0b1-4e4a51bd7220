// <PERSON>'s Meeting Note - 主要JavaScript功能

// 纠错词典管理类
class CorrectionDictionary {
    constructor() {
        this.corrections = this.loadCorrections();
        this.statistics = this.loadStatistics();
    }

    // 加载纠错词典
    loadCorrections() {
        const saved = localStorage.getItem('correctionDictionary');
        return saved ? JSON.parse(saved) : {};
    }

    // 加载统计信息
    loadStatistics() {
        const saved = localStorage.getItem('correctionStatistics');
        return saved ? JSON.parse(saved) : {
            totalCorrections: 0,
            wordsLearned: 0,
            lastUpdated: null
        };
    }

    // 保存纠错词典
    saveCorrections() {
        localStorage.setItem('correctionDictionary', JSON.stringify(this.corrections));
        this.updateStatistics();
    }

    // 更新统计信息
    updateStatistics() {
        this.statistics.wordsLearned = Object.keys(this.corrections).length;
        this.statistics.lastUpdated = new Date().toISOString();
        localStorage.setItem('correctionStatistics', JSON.stringify(this.statistics));
    }

    // 添加纠错映射
    addCorrection(wrongWord, correctWord, context = '') {
        const key = wrongWord.toLowerCase().trim();
        if (!this.corrections[key]) {
            this.corrections[key] = {
                correct: correctWord.trim(),
                frequency: 1,
                contexts: [context],
                createdAt: new Date().toISOString(),
                lastUsed: new Date().toISOString()
            };
        } else {
            this.corrections[key].frequency++;
            this.corrections[key].lastUsed = new Date().toISOString();
            if (context && !this.corrections[key].contexts.includes(context)) {
                this.corrections[key].contexts.push(context);
            }
        }

        this.statistics.totalCorrections++;
        this.saveCorrections();
        console.log(`学习新纠错: "${wrongWord}" → "${correctWord}"`);
    }

    // 获取纠正建议
    getCorrection(word) {
        const key = word.toLowerCase().trim();
        return this.corrections[key] ? this.corrections[key].correct : null;
    }

    // 模糊匹配纠错
    fuzzyMatch(word, threshold = 0.7) {
        const key = word.toLowerCase().trim();
        let bestMatch = null;
        let bestScore = 0;

        for (const wrongWord in this.corrections) {
            const similarity = this.calculateSimilarity(key, wrongWord);
            if (similarity > threshold && similarity > bestScore) {
                bestScore = similarity;
                bestMatch = this.corrections[wrongWord].correct;
            }
        }

        return bestMatch;
    }

    // 计算字符串相似度（Levenshtein距离）
    calculateSimilarity(str1, str2) {
        const len1 = str1.length;
        const len2 = str2.length;
        const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));

        for (let i = 0; i <= len1; i++) matrix[i][0] = i;
        for (let j = 0; j <= len2; j++) matrix[0][j] = j;

        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                if (str1[i - 1] === str2[j - 1]) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j - 1] + 1
                    );
                }
            }
        }

        const maxLen = Math.max(len1, len2);
        return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen;
    }

    // 应用纠错到文本
    applyCorrections(text) {
        let correctedText = text;
        let appliedCorrections = [];

        // 直接匹配
        for (const wrongWord in this.corrections) {
            const correctWord = this.corrections[wrongWord].correct;
            const regex = new RegExp(`\\b${this.escapeRegex(wrongWord)}\\b`, 'gi');
            if (regex.test(correctedText)) {
                correctedText = correctedText.replace(regex, correctWord);
                appliedCorrections.push({ wrong: wrongWord, correct: correctWord, type: 'exact' });
            }
        }

        // 模糊匹配（可选，性能考虑）
        const words = correctedText.split(/\s+/);
        for (let i = 0; i < words.length; i++) {
            const word = words[i].replace(/[^\w\u4e00-\u9fff]/g, '');
            if (word.length > 2) {
                const fuzzyCorrection = this.fuzzyMatch(word, 0.8);
                if (fuzzyCorrection && !appliedCorrections.some(c => c.correct === fuzzyCorrection)) {
                    words[i] = words[i].replace(word, fuzzyCorrection);
                    appliedCorrections.push({ wrong: word, correct: fuzzyCorrection, type: 'fuzzy' });
                }
            }
        }

        if (appliedCorrections.length > 0) {
            correctedText = words.join(' ');
            console.log('应用纠错:', appliedCorrections);
        }

        return { text: correctedText, corrections: appliedCorrections };
    }

    // 转义正则表达式特殊字符
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // 获取统计信息
    getStatistics() {
        return {
            ...this.statistics,
            wordsLearned: Object.keys(this.corrections).length
        };
    }

    // 获取所有纠错记录
    getAllCorrections() {
        return Object.entries(this.corrections).map(([wrong, data]) => ({
            wrong,
            correct: data.correct,
            frequency: data.frequency,
            contexts: data.contexts,
            createdAt: data.createdAt,
            lastUsed: data.lastUsed
        }));
    }

    // 删除纠错记录
    removeCorrection(wrongWord) {
        const key = wrongWord.toLowerCase().trim();
        if (this.corrections[key]) {
            delete this.corrections[key];
            this.saveCorrections();
            return true;
        }
        return false;
    }

    // 清空词典
    clearDictionary() {
        this.corrections = {};
        this.statistics = {
            totalCorrections: 0,
            wordsLearned: 0,
            lastUpdated: null
        };
        this.saveCorrections();
    }

    // 导出词典
    exportDictionary() {
        return {
            corrections: this.corrections,
            statistics: this.statistics,
            exportedAt: new Date().toISOString(),
            version: '1.0'
        };
    }

    // 导入词典
    importDictionary(data) {
        if (data.corrections && typeof data.corrections === 'object') {
            this.corrections = { ...this.corrections, ...data.corrections };
            this.saveCorrections();
            return true;
        }
        return false;
    }
}

// 智能纠错处理类
class SmartCorrector {
    constructor(dictionary) {
        this.dictionary = dictionary;
        this.isEnabled = true;
    }

    // 预处理文本（发送给AI前）
    preprocessText(text) {
        if (!this.isEnabled) return text;

        const result = this.dictionary.applyCorrections(text);
        if (result.corrections.length > 0) {
            console.log('预处理应用纠错:', result.corrections.length, '个');
        }
        return result.text;
    }

    // 后处理文本（AI返回后）
    postprocessText(text) {
        if (!this.isEnabled) return text;

        const result = this.dictionary.applyCorrections(text);
        if (result.corrections.length > 0) {
            console.log('后处理应用纠错:', result.corrections.length, '个');
        }
        return result.text;
    }

    // 检测可能的错误词汇
    detectPotentialErrors(text) {
        const words = text.split(/\s+/);
        const potentialErrors = [];

        words.forEach((word, index) => {
            const cleanWord = word.replace(/[^\w\u4e00-\u9fff]/g, '');
            if (cleanWord.length > 2) {
                const fuzzyMatch = this.dictionary.fuzzyMatch(cleanWord, 0.6);
                if (fuzzyMatch) {
                    potentialErrors.push({
                        word: cleanWord,
                        suggestion: fuzzyMatch,
                        position: index,
                        confidence: 0.6
                    });
                }
            }
        });

        return potentialErrors;
    }

    // 启用/禁用纠错
    setEnabled(enabled) {
        this.isEnabled = enabled;
        console.log('智能纠错', enabled ? '已启用' : '已禁用');
    }
}

// AI配置管理类
class AIConfigManager {
    constructor() {
        this.apiConfigs = {
            deepseek: {
                name: 'DeepSeek',
                baseUrl: 'https://api.deepseek.com/v1',
                models: ['deepseek-chat', 'deepseek-coder'],
                headers: { 'Authorization': 'Bearer {apiKey}' }
            },
            minimax: {
                name: 'MiniMax',
                baseUrl: 'https://api.minimax.chat/v1',
                models: ['abab6.5s-chat', 'abab6.5-chat', 'abab5.5s-chat'],
                headers: { 'Authorization': 'Bearer {apiKey}' }
            },
            doubao: {
                name: '豆包(字节跳动)',
                baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
                models: ['ep-20241217-*****', 'ep-20241217-*****'],
                headers: { 'Authorization': 'Bearer {apiKey}' }
            },
            gemini: {
                name: 'Google Gemini',
                baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
                models: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro'],
                headers: { 'x-goog-api-key': '{apiKey}' }
            },
            openai: {
                name: 'OpenAI GPT',
                baseUrl: 'https://api.openai.com/v1',
                models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
                headers: { 'Authorization': 'Bearer {apiKey}' }
            },
            custom: {
                name: '自定义API',
                baseUrl: '',
                models: [],
                headers: { 'Authorization': 'Bearer {apiKey}' }
            }
        };

        this.currentConfig = this.loadConfig();
    }

    // 加载保存的配置
    loadConfig() {
        const saved = localStorage.getItem('aiConfig');
        return saved ? JSON.parse(saved) : null;
    }

    // 保存配置
    saveConfig(config) {
        this.currentConfig = config;
        localStorage.setItem('aiConfig', JSON.stringify(config));
    }

    // 获取API配置
    getApiConfig(provider) {
        return this.apiConfigs[provider] || null;
    }

    // 获取当前配置
    getCurrentConfig() {
        return this.currentConfig;
    }

    // 清空配置
    clearConfig() {
        this.currentConfig = null;
        localStorage.removeItem('aiConfig');
    }
}

// AI服务调用类
class AIService {
    constructor(configManager) {
        this.configManager = configManager;
        this.requestTimeout = 30000; // 30秒超时
    }

    // 测试API连接
    async testConnection(config) {
        try {
            const testPrompt = "Hello, this is a connection test.";
            const response = await this.makeRequest(config, testPrompt, { maxTokens: 10 });
            return { success: true, message: '连接成功' };
        } catch (error) {
            return { success: false, message: error.message };
        }
    }

    // 生成会议纪要
    async generateSummary(config, content, mode, customPrompt = '') {
        const prompts = {
            'ai-concise': '请将以下会议内容总结为简洁的纪要，包含主要讨论点和决策：',
            'ai-detailed': '请将以下会议内容整理为详细的会议纪要，包含完整的讨论过程、决策和行动项：',
            'ai-action': '请从以下会议内容中提取所有行动项、决策和待办事项，并按优先级整理：',
            'ai-custom': customPrompt || '请总结以下会议内容：'
        };

        const prompt = prompts[mode] + '\n\n' + content;
        return await this.makeRequest(config, prompt);
    }

    // 发送API请求
    async makeRequest(config, prompt, options = {}) {
        const { provider, apiKey, baseUrl, model, temperature = 0.7, maxTokens = 2000 } = config;

        if (!apiKey) {
            throw new Error('API Key未配置');
        }

        const apiConfig = this.configManager.getApiConfig(provider);
        if (!apiConfig) {
            throw new Error('不支持的API服务商');
        }

        const url = (baseUrl || apiConfig.baseUrl);
        const headers = { ...apiConfig.headers };

        // 替换API Key
        Object.keys(headers).forEach(key => {
            headers[key] = headers[key].replace('{apiKey}', apiKey);
        });

        const requestBody = this.buildRequestBody(provider, prompt, model, {
            temperature: options.temperature || temperature,
            maxTokens: options.maxTokens || maxTokens
        });

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

        try {
            const response = await fetch(this.getEndpoint(url, provider), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...headers
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API请求失败: ${response.status} - ${errorData.error?.message || response.statusText}`);
            }

            const data = await response.json();
            return this.extractResponse(provider, data);

        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请检查网络连接');
            }
            throw error;
        }
    }

    // 构建请求体
    buildRequestBody(provider, prompt, model, options) {
        const baseBody = {
            model: model,
            temperature: options.temperature,
            max_tokens: options.maxTokens
        };

        switch (provider) {
            case 'openai':
            case 'deepseek':
            case 'minimax':
            case 'doubao':
                return {
                    ...baseBody,
                    messages: [{ role: 'user', content: prompt }]
                };
            case 'gemini':
                return {
                    contents: [{ parts: [{ text: prompt }] }],
                    generationConfig: {
                        temperature: options.temperature,
                        maxOutputTokens: options.maxTokens
                    }
                };
            default:
                return {
                    ...baseBody,
                    messages: [{ role: 'user', content: prompt }]
                };
        }
    }

    // 获取API端点
    getEndpoint(baseUrl, provider) {
        switch (provider) {
            case 'gemini':
                return `${baseUrl}/models/gemini-pro:generateContent`;
            default:
                return `${baseUrl}/chat/completions`;
        }
    }

    // 提取响应内容
    extractResponse(provider, data) {
        switch (provider) {
            case 'gemini':
                return data.candidates?.[0]?.content?.parts?.[0]?.text || '无响应内容';
            default:
                return data.choices?.[0]?.message?.content || '无响应内容';
        }
    }
}

class MeetingNoteApp {
    constructor() {
        this.recognition = null;
        this.isRecording = false;
        this.isPaused = false;
        this.transcriptionText = '';
        this.currentMeeting = null;
        this.isEditMode = false;

        // 初始化AI功能
        this.aiConfigManager = new AIConfigManager();
        this.aiService = new AIService(this.aiConfigManager);

        // 初始化纠错功能
        this.correctionDictionary = new CorrectionDictionary();
        this.smartCorrector = new SmartCorrector(this.correctionDictionary);

        this.initializeApp();
        this.setupEventListeners();
        this.checkBrowserSupport();
    }

    // 初始化应用
    initializeApp() {
        console.log('Jason\'s Meeting Note 应用初始化中...');
        this.loadSavedMeetings();
        this.setDefaultDateTime();
        this.initializeAISettings();
        this.initializeCorrectionFeatures();
    }

    // 初始化AI设置
    initializeAISettings() {
        const savedConfig = this.aiConfigManager.getCurrentConfig();
        if (savedConfig) {
            this.loadAIConfig(savedConfig);
        }
        this.updateConnectionStatus('unknown', '未测试');
    }

    // 初始化纠错功能
    initializeCorrectionFeatures() {
        this.updateCorrectionStats();
        this.loadCorrectionSettings();
        console.log('纠错功能已初始化');
    }

    // 检查浏览器支持
    checkBrowserSupport() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            this.showModal('抱歉，您的浏览器不支持语音识别功能。请使用Chrome、Edge或Safari浏览器。');
            document.getElementById('startRecording').disabled = true;
            return false;
        }
        return true;
    }

    // 设置默认日期时间
    setDefaultDateTime() {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
            .toISOString().slice(0, 16);
        document.getElementById('meetingTime').value = localDateTime;
    }

    // 设置事件监听器
    setupEventListeners() {
        // 语音控制按钮
        document.getElementById('startRecording').addEventListener('click', () => this.startRecording());
        document.getElementById('stopRecording').addEventListener('click', () => this.stopRecording());
        document.getElementById('pauseRecording').addEventListener('click', () => this.pauseRecording());

        // 清空按钮
        document.getElementById('clearTranscription').addEventListener('click', () => this.clearTranscription());
        document.getElementById('clearManualNotes').addEventListener('click', () => this.clearManualNotes());

        // 时间戳按钮
        document.getElementById('addTimestamp').addEventListener('click', () => this.addTimestamp());

        // 生成纪要按钮
        document.getElementById('generateSummary').addEventListener('click', () => this.generateSummary());

        // 操作按钮
        document.getElementById('saveMeeting').addEventListener('click', () => this.saveMeeting());
        document.getElementById('exportMeeting').addEventListener('click', () => this.exportMeeting());
        document.getElementById('newMeeting').addEventListener('click', () => this.newMeeting());
        document.getElementById('loadMeeting').addEventListener('click', () => this.toggleHistorySection());

        // AI设置相关事件
        const toggleAiButton = document.getElementById('toggleAiSettings');
        if (toggleAiButton) {
            toggleAiButton.addEventListener('click', () => {
                console.log('AI配置按钮被点击');
                this.toggleAISettings();
            });
            console.log('AI配置按钮事件监听器已绑定');
        } else {
            console.error('未找到AI配置按钮 (toggleAiSettings)');
        }

        // 其他AI相关事件监听器（添加存在性检查）
        const apiProvider = document.getElementById('apiProvider');
        if (apiProvider) {
            apiProvider.addEventListener('change', (e) => this.onApiProviderChange(e.target.value));
            console.log('API服务商选择事件监听器已绑定');
        }

        const toggleApiKey = document.getElementById('toggleApiKey');
        if (toggleApiKey) {
            toggleApiKey.addEventListener('click', () => this.toggleApiKeyVisibility());
            console.log('API Key显示切换事件监听器已绑定');
        }

        const toggleAdvanced = document.getElementById('toggleAdvanced');
        if (toggleAdvanced) {
            toggleAdvanced.addEventListener('click', () => this.toggleAdvancedParams());
            console.log('高级参数切换事件监听器已绑定');
        }

        const testConnection = document.getElementById('testConnection');
        if (testConnection) {
            testConnection.addEventListener('click', () => this.testAPIConnection());
            console.log('测试连接事件监听器已绑定');
        }

        const saveAiConfig = document.getElementById('saveAiConfig');
        if (saveAiConfig) {
            saveAiConfig.addEventListener('click', () => this.saveAIConfig());
            console.log('保存AI配置事件监听器已绑定');
        }

        const clearAiConfig = document.getElementById('clearAiConfig');
        if (clearAiConfig) {
            clearAiConfig.addEventListener('click', () => this.clearAIConfig());
            console.log('清空AI配置事件监听器已绑定');
        }

        // 纪要生成模式切换
        const summaryMode = document.getElementById('summaryMode');
        if (summaryMode) {
            summaryMode.addEventListener('change', (e) => this.onSummaryModeChange(e.target.value));
            console.log('纪要生成模式切换事件监听器已绑定');
        }

        // 高级参数滑块
        const temperature = document.getElementById('temperature');
        const temperatureValue = document.getElementById('temperatureValue');
        if (temperature && temperatureValue) {
            temperature.addEventListener('input', (e) => {
                temperatureValue.textContent = e.target.value;
            });
            console.log('温度参数滑块事件监听器已绑定');
        }

        // 纠错功能相关事件
        const toggleEditMode = document.getElementById('toggleEditMode');
        if (toggleEditMode) {
            toggleEditMode.addEventListener('click', () => this.toggleEditMode());
        }

        const applyCorrections = document.getElementById('applyCorrections');
        if (applyCorrections) {
            applyCorrections.addEventListener('click', () => this.applyPendingCorrections());
        }

        const cancelEdit = document.getElementById('cancelEdit');
        if (cancelEdit) {
            cancelEdit.addEventListener('click', () => this.cancelEditMode());
        }

        const showDictionary = document.getElementById('showDictionary');
        if (showDictionary) {
            showDictionary.addEventListener('click', () => this.showDictionaryModal());
        }

        // 词典管理相关事件
        this.setupDictionaryEventListeners();

        // 模态框关闭
        document.querySelector('.close').addEventListener('click', () => this.closeModal());
        window.addEventListener('click', (event) => {
            const modal = document.getElementById('statusModal');
            if (event.target === modal) {
                this.closeModal();
            }
        });
    }

    // 开始录音
    startRecording() {
        if (!this.checkBrowserSupport()) return;

        try {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            
            // 配置语音识别
            this.recognition.continuous = true;
            this.recognition.interimResults = true;
            this.recognition.lang = 'zh-CN'; // 默认中文，可以根据需要调整
            
            // 语音识别事件处理
            this.recognition.onstart = () => {
                this.isRecording = true;
                this.isPaused = false;
                this.updateRecordingUI();
                console.log('语音识别已开始');
            };
            
            this.recognition.onresult = (event) => {
                let interimTranscript = '';
                let finalTranscript = '';
                
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    if (event.results[i].isFinal) {
                        finalTranscript += transcript + ' ';
                    } else {
                        interimTranscript += transcript;
                    }
                }
                
                this.updateTranscriptionDisplay(finalTranscript, interimTranscript);
            };
            
            this.recognition.onerror = (event) => {
                console.error('语音识别错误:', event.error);
                this.showModal(`语音识别出现错误: ${event.error}`);
                this.stopRecording();
            };
            
            this.recognition.onend = () => {
                if (this.isRecording && !this.isPaused) {
                    // 如果还在录音状态但识别结束了，重新开始
                    setTimeout(() => {
                        if (this.isRecording) {
                            this.recognition.start();
                        }
                    }, 100);
                }
            };
            
            this.recognition.start();
            
        } catch (error) {
            console.error('启动语音识别失败:', error);
            this.showModal('启动语音识别失败，请检查麦克风权限。');
        }
    }

    // 停止录音
    stopRecording() {
        if (this.recognition) {
            this.isRecording = false;
            this.isPaused = false;
            this.recognition.stop();
            this.updateRecordingUI();
            console.log('语音识别已停止');
        }
    }

    // 暂停录音
    pauseRecording() {
        if (this.recognition && this.isRecording) {
            this.isPaused = !this.isPaused;
            
            if (this.isPaused) {
                this.recognition.stop();
            } else {
                this.recognition.start();
            }
            
            this.updateRecordingUI();
        }
    }

    // 更新录音UI状态
    updateRecordingUI() {
        const startBtn = document.getElementById('startRecording');
        const stopBtn = document.getElementById('stopRecording');
        const pauseBtn = document.getElementById('pauseRecording');
        const status = document.getElementById('recordingStatus');
        
        if (this.isRecording) {
            if (this.isPaused) {
                startBtn.disabled = false;
                stopBtn.disabled = false;
                pauseBtn.disabled = false;
                pauseBtn.innerHTML = '<i class="fas fa-play"></i> 继续';
                status.textContent = '已暂停';
                status.className = 'status-indicator paused';
            } else {
                startBtn.disabled = true;
                stopBtn.disabled = false;
                pauseBtn.disabled = false;
                pauseBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
                status.textContent = '正在录音';
                status.className = 'status-indicator recording';
            }
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            pauseBtn.disabled = true;
            pauseBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
            status.textContent = '准备就绪';
            status.className = 'status-indicator';
        }
    }

    // 更新转录显示
    updateTranscriptionDisplay(finalText, interimText) {
        const transcriptionDiv = document.getElementById('transcriptionText');

        if (finalText) {
            // 应用智能纠错
            const correctedResult = this.smartCorrector.preprocessText(finalText);
            this.transcriptionText += correctedResult;
        }

        const displayText = this.transcriptionText + (interimText ? `<span style="color: #999; font-style: italic;">${interimText}</span>` : '');

        if (displayText.trim()) {
            transcriptionDiv.innerHTML = `<div style="white-space: pre-wrap;">${displayText}</div>`;
        } else {
            transcriptionDiv.innerHTML = '<p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>';
        }

        // 自动滚动到底部
        transcriptionDiv.scrollTop = transcriptionDiv.scrollHeight;
    }

    // 清空转录内容
    clearTranscription() {
        this.transcriptionText = '';
        document.getElementById('transcriptionText').innerHTML = '<p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>';
    }

    // 清空手动笔记
    clearManualNotes() {
        document.getElementById('manualNotes').value = '';
    }

    // 添加时间戳
    addTimestamp() {
        const textarea = document.getElementById('manualNotes');
        const now = new Date();
        const timestamp = `[${now.toLocaleTimeString()}] `;

        const cursorPos = textarea.selectionStart;
        const textBefore = textarea.value.substring(0, cursorPos);
        const textAfter = textarea.value.substring(cursorPos);

        textarea.value = textBefore + timestamp + textAfter;
        textarea.focus();
        textarea.setSelectionRange(cursorPos + timestamp.length, cursorPos + timestamp.length);
    }

    // AI设置相关方法

    // 切换AI设置面板
    toggleAISettings() {
        const panel = document.getElementById('aiSettingsPanel');
        const button = document.getElementById('toggleAiSettings');

        if (!panel || !button) {
            console.error('AI设置面板或按钮未找到');
            return;
        }

        // 获取当前显示状态
        const isVisible = panel.style.display === 'block';

        // 切换显示状态
        panel.style.display = isVisible ? 'none' : 'block';

        // 更新按钮文本
        button.innerHTML = isVisible ?
            '<i class="fas fa-cog"></i> 配置AI' :
            '<i class="fas fa-times"></i> 关闭配置';

        console.log('AI设置面板状态:', isVisible ? '关闭' : '打开');
    }

    // API服务商变更
    onApiProviderChange(provider) {
        const configArea = document.getElementById('apiConfigArea');
        const modelSelect = document.getElementById('modelName');
        const baseUrlInput = document.getElementById('apiBaseUrl');

        if (provider) {
            configArea.style.display = 'block';

            const apiConfig = this.aiConfigManager.getApiConfig(provider);
            if (apiConfig) {
                // 更新模型选项
                modelSelect.innerHTML = '<option value="">请选择模型</option>';
                apiConfig.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    modelSelect.appendChild(option);
                });

                // 设置默认Base URL
                baseUrlInput.value = apiConfig.baseUrl;
            }
        } else {
            configArea.style.display = 'none';
        }

        this.updateConnectionStatus('unknown', '未测试');
    }

    // 切换API Key可见性
    toggleApiKeyVisibility() {
        const input = document.getElementById('apiKey');
        const button = document.getElementById('toggleApiKey');

        if (input.type === 'password') {
            input.type = 'text';
            button.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            input.type = 'password';
            button.innerHTML = '<i class="fas fa-eye"></i>';
        }
    }

    // 切换高级参数
    toggleAdvancedParams() {
        const params = document.getElementById('advancedParams');
        const button = document.getElementById('toggleAdvanced');
        const isVisible = params.style.display !== 'none';

        params.style.display = isVisible ? 'none' : 'block';
        button.innerHTML = isVisible ?
            '<i class="fas fa-sliders-h"></i> 高级参数' :
            '<i class="fas fa-sliders-h"></i> 隐藏参数';
    }

    // 纪要生成模式变更
    onSummaryModeChange(mode) {
        const customArea = document.getElementById('customPromptArea');
        customArea.style.display = mode === 'ai-custom' ? 'block' : 'none';
    }

    // 测试API连接
    async testAPIConnection() {
        const config = this.getAIConfigFromForm();
        if (!config.apiKey) {
            this.showModal('请先输入API Key');
            return;
        }

        this.updateConnectionStatus('testing', '测试中...');

        try {
            const result = await this.aiService.testConnection(config);
            if (result.success) {
                this.updateConnectionStatus('connected', '连接成功');
                this.showModal('API连接测试成功！');
            } else {
                this.updateConnectionStatus('error', '连接失败');
                this.showModal(`连接测试失败: ${result.message}`);
            }
        } catch (error) {
            this.updateConnectionStatus('error', '连接失败');
            this.showModal(`连接测试失败: ${error.message}`);
        }
    }

    // 保存AI配置
    saveAIConfig() {
        const config = this.getAIConfigFromForm();
        if (!config.provider || !config.apiKey) {
            this.showModal('请完整填写API服务商和API Key');
            return;
        }

        this.aiConfigManager.saveConfig(config);
        this.showModal('AI配置已保存成功！');
    }

    // 清空AI配置
    clearAIConfig() {
        if (confirm('确定要清空AI配置吗？')) {
            this.aiConfigManager.clearConfig();
            this.clearAIConfigForm();
            this.updateConnectionStatus('unknown', '未测试');
            this.showModal('AI配置已清空');
        }
    }

    // 从表单获取AI配置
    getAIConfigFromForm() {
        return {
            provider: document.getElementById('apiProvider').value,
            apiKey: document.getElementById('apiKey').value,
            baseUrl: document.getElementById('apiBaseUrl').value,
            model: document.getElementById('modelName').value,
            temperature: parseFloat(document.getElementById('temperature').value),
            maxTokens: parseInt(document.getElementById('maxTokens').value)
        };
    }

    // 加载AI配置到表单
    loadAIConfig(config) {
        document.getElementById('apiProvider').value = config.provider || '';
        document.getElementById('apiKey').value = config.apiKey || '';
        document.getElementById('apiBaseUrl').value = config.baseUrl || '';
        document.getElementById('modelName').value = config.model || '';
        document.getElementById('temperature').value = config.temperature || 0.7;
        document.getElementById('temperatureValue').textContent = config.temperature || 0.7;
        document.getElementById('maxTokens').value = config.maxTokens || 2000;

        if (config.provider) {
            this.onApiProviderChange(config.provider);
        }
    }

    // 清空AI配置表单
    clearAIConfigForm() {
        document.getElementById('apiProvider').value = '';
        document.getElementById('apiKey').value = '';
        document.getElementById('apiBaseUrl').value = '';
        document.getElementById('modelName').innerHTML = '<option value="">请先选择API服务商</option>';
        document.getElementById('temperature').value = 0.7;
        document.getElementById('temperatureValue').textContent = '0.7';
        document.getElementById('maxTokens').value = 2000;
        document.getElementById('apiConfigArea').style.display = 'none';
    }

    // 更新连接状态
    updateConnectionStatus(status, text) {
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.querySelector('.status-text');

        statusDot.className = `status-dot status-${status}`;
        statusText.textContent = text;
    }

    // 纠错功能相关方法

    // 切换纠错模式
    toggleEditMode() {
        this.isEditMode = !this.isEditMode;
        const summaryDisplay = document.getElementById('meetingSummary');
        const toggleButton = document.getElementById('toggleEditMode');
        const applyButton = document.getElementById('applyCorrections');
        const cancelButton = document.getElementById('cancelEdit');

        if (this.isEditMode) {
            summaryDisplay.classList.add('edit-mode');
            this.makeContentEditable();
            toggleButton.style.display = 'none';
            applyButton.style.display = 'inline-flex';
            cancelButton.style.display = 'inline-flex';
            this.showCorrectionSuggestions();
        } else {
            summaryDisplay.classList.remove('edit-mode');
            this.makeContentReadonly();
            toggleButton.style.display = 'inline-flex';
            applyButton.style.display = 'none';
            cancelButton.style.display = 'none';
            this.hideCorrectionSuggestions();
        }

        console.log('纠错模式:', this.isEditMode ? '开启' : '关闭');
    }

    // 使内容可编辑
    makeContentEditable() {
        const summaryDisplay = document.getElementById('meetingSummary');
        const content = summaryDisplay.innerHTML;

        if (content && !content.includes('placeholder-text')) {
            // 将文本内容包装为可编辑的词汇
            const editableContent = this.wrapWordsForEditing(content);
            summaryDisplay.innerHTML = editableContent;

            // 为可编辑词汇添加事件监听器
            this.addWordClickListeners();
        }
    }

    // 包装词汇为可编辑元素
    wrapWordsForEditing(content) {
        // 移除HTML标签，保留文本内容
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = content;
        const textContent = tempDiv.textContent || tempDiv.innerText || '';

        // 将文本分割为词汇并包装
        const words = textContent.split(/(\s+|[，。！？；：""''（）【】《》、])/);
        return words.map((word, index) => {
            if (word.trim() && !/^\s+$/.test(word) && !/^[，。！？；：""''（）【】《》、]+$/.test(word)) {
                return `<span class="editable-word" data-word="${word}" data-index="${index}">${word}</span>`;
            }
            return word;
        }).join('');
    }

    // 为词汇添加点击事件监听器
    addWordClickListeners() {
        const editableWords = document.querySelectorAll('.editable-word');
        editableWords.forEach(wordElement => {
            wordElement.addEventListener('click', (e) => {
                e.preventDefault();
                this.selectWordForCorrection(wordElement);
            });
        });
    }

    // 选择词汇进行纠错
    selectWordForCorrection(wordElement) {
        // 移除其他选中状态
        document.querySelectorAll('.editable-word.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // 选中当前词汇
        wordElement.classList.add('selected');

        const word = wordElement.dataset.word;
        this.showQuickCorrectionModal(word, wordElement);
    }

    // 显示快速纠错模态框
    showQuickCorrectionModal(word, wordElement) {
        const modal = document.getElementById('quickCorrectionModal');
        const wrongWordInput = document.getElementById('wrongWord');
        const correctWordInput = document.getElementById('correctWord');
        const contextTextarea = document.getElementById('correctionContext');

        wrongWordInput.value = word;
        correctWordInput.value = '';
        contextTextarea.value = '';

        // 检查是否有已知的纠错建议
        const suggestion = this.correctionDictionary.getCorrection(word);
        if (suggestion) {
            correctWordInput.value = suggestion;
        }

        modal.style.display = 'block';
        correctWordInput.focus();

        // 保存当前选中的元素引用
        this.currentEditingElement = wordElement;
    }

    // 使内容只读
    makeContentReadonly() {
        const summaryDisplay = document.getElementById('meetingSummary');
        const editableWords = summaryDisplay.querySelectorAll('.editable-word');

        if (editableWords.length > 0) {
            // 提取纯文本内容
            const textContent = Array.from(editableWords).map(el => el.textContent).join('');
            // 这里可以重新生成格式化的HTML内容
            // 为简化，暂时保持当前内容
        }

        summaryDisplay.classList.remove('edit-mode');
    }

    // 显示纠错建议
    showCorrectionSuggestions() {
        const suggestionsPanel = document.getElementById('correctionSuggestions');
        const summaryContent = document.getElementById('meetingSummary').textContent;

        if (summaryContent && !summaryContent.includes('placeholder-text')) {
            const potentialErrors = this.smartCorrector.detectPotentialErrors(summaryContent);

            if (potentialErrors.length > 0) {
                this.renderCorrectionSuggestions(potentialErrors);
                suggestionsPanel.style.display = 'block';
            } else {
                suggestionsPanel.style.display = 'none';
            }
        }
    }

    // 渲染纠错建议
    renderCorrectionSuggestions(suggestions) {
        const suggestionsList = document.getElementById('suggestionsList');

        suggestionsList.innerHTML = suggestions.map(suggestion => `
            <div class="suggestion-item">
                <div class="suggestion-text">
                    <span class="suggestion-wrong">${suggestion.word}</span>
                    <span>→</span>
                    <span class="suggestion-correct">${suggestion.suggestion}</span>
                </div>
                <div class="suggestion-actions">
                    <button class="btn btn-sm btn-success" onclick="meetingApp.applySuggestion('${suggestion.word}', '${suggestion.suggestion}')">
                        <i class="fas fa-check"></i> 应用
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="meetingApp.ignoreSuggestion('${suggestion.word}')">
                        <i class="fas fa-times"></i> 忽略
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 应用建议的纠错
    applySuggestion(wrongWord, correctWord) {
        this.correctionDictionary.addCorrection(wrongWord, correctWord, '自动建议');
        this.updateCorrectionStats();

        // 在当前内容中应用纠错
        const summaryDisplay = document.getElementById('meetingSummary');
        const content = summaryDisplay.innerHTML;
        const correctedContent = content.replace(
            new RegExp(`\\b${this.escapeRegex(wrongWord)}\\b`, 'gi'),
            correctWord
        );
        summaryDisplay.innerHTML = correctedContent;

        // 重新显示建议
        this.showCorrectionSuggestions();
        this.showModal(`已学习纠错: "${wrongWord}" → "${correctWord}"`);
    }

    // 忽略建议
    ignoreSuggestion(word) {
        // 从建议列表中移除
        const suggestionItems = document.querySelectorAll('.suggestion-item');
        suggestionItems.forEach(item => {
            if (item.textContent.includes(word)) {
                item.remove();
            }
        });
    }

    // 隐藏纠错建议
    hideCorrectionSuggestions() {
        const suggestionsPanel = document.getElementById('correctionSuggestions');
        suggestionsPanel.style.display = 'none';
    }

    // 生成会议纪要
    async generateSummary() {
        const meetingTitle = document.getElementById('meetingTitle').value || '未命名会议';
        const meetingTime = document.getElementById('meetingTime').value;
        const participants = document.getElementById('participants').value;
        const transcription = this.transcriptionText;
        const manualNotes = document.getElementById('manualNotes').value;
        const summaryMode = document.getElementById('summaryMode').value;

        if (!transcription.trim() && !manualNotes.trim()) {
            this.showModal('请先录制语音或输入手动笔记后再生成纪要。');
            return;
        }

        // 基础模式直接生成
        if (summaryMode === 'basic') {
            const summary = this.createMeetingSummary({
                title: meetingTitle,
                time: meetingTime,
                participants: participants,
                transcription: transcription,
                manualNotes: manualNotes
            });
            document.getElementById('meetingSummary').innerHTML = summary;
            return;
        }

        // AI模式需要检查配置
        const aiConfig = this.aiConfigManager.getCurrentConfig();
        if (!aiConfig || !aiConfig.apiKey) {
            this.showModal('请先配置AI服务后再使用AI生成功能。');
            return;
        }

        // 显示进度指示器
        this.showGenerationProgress(true);

        try {
            // 准备内容
            const content = this.prepareContentForAI(meetingTitle, meetingTime, participants, transcription, manualNotes);

            // 获取自定义提示词
            const customPrompt = summaryMode === 'ai-custom' ?
                document.getElementById('customPrompt').value : '';

            // 调用AI生成
            const aiSummary = await this.aiService.generateSummary(aiConfig, content, summaryMode, customPrompt);

            // 生成最终纪要
            const summary = this.createAIMeetingSummary({
                title: meetingTitle,
                time: meetingTime,
                participants: participants,
                transcription: transcription,
                manualNotes: manualNotes,
                aiSummary: aiSummary,
                mode: summaryMode
            });

            document.getElementById('meetingSummary').innerHTML = summary;

        } catch (error) {
            console.error('AI生成失败:', error);
            this.showModal(`AI生成失败: ${error.message}`);

            // 失败时回退到基础模式
            const summary = this.createMeetingSummary({
                title: meetingTitle,
                time: meetingTime,
                participants: participants,
                transcription: transcription,
                manualNotes: manualNotes
            });
            document.getElementById('meetingSummary').innerHTML = summary;

        } finally {
            this.showGenerationProgress(false);
        }
    }

    // 准备AI处理的内容
    prepareContentForAI(title, time, participants, transcription, manualNotes) {
        let content = `会议主题: ${title}\n`;
        if (time) {
            const date = new Date(time);
            content += `会议时间: ${date.toLocaleString('zh-CN')}\n`;
        }
        if (participants) {
            content += `参会人员: ${participants}\n`;
        }
        content += '\n';

        if (transcription.trim()) {
            // 对转录内容应用纠错
            const correctedTranscription = this.smartCorrector.preprocessText(transcription);
            content += '语音转录内容:\n' + correctedTranscription + '\n\n';
        }

        if (manualNotes.trim()) {
            // 对手动笔记应用纠错
            const correctedNotes = this.smartCorrector.preprocessText(manualNotes);
            content += '手动记录内容:\n' + correctedNotes + '\n';
        }

        return content;
    }

    // 显示/隐藏生成进度
    showGenerationProgress(show) {
        const progress = document.getElementById('generationProgress');
        progress.style.display = show ? 'block' : 'none';

        // 禁用生成按钮
        const button = document.getElementById('generateSummary');
        button.disabled = show;
        button.innerHTML = show ?
            '<i class="fas fa-spinner fa-spin"></i> AI生成中...' :
            '<i class="fas fa-magic"></i> 生成纪要';
    }

    // 创建AI增强的会议纪要
    createAIMeetingSummary(data) {
        const formatTime = (timeString) => {
            if (!timeString) return '未设置';
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN');
        };

        const modeNames = {
            'ai-concise': 'AI简洁版',
            'ai-detailed': 'AI详细版',
            'ai-action': 'AI行动项提取',
            'ai-custom': 'AI自定义'
        };

        // 对AI生成的内容应用后处理纠错
        const correctedAISummary = this.smartCorrector.postprocessText(data.aiSummary);

        const summary = `
            <div class="summary-content">
                <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px;">
                    📋 ${data.title}
                </h4>

                <div style="margin-bottom: 20px;">
                    <p><strong>🕐 会议时间：</strong>${formatTime(data.time)}</p>
                    <p><strong>👥 参会人员：</strong>${data.participants || '未填写'}</p>
                    <p><strong>🤖 生成模式：</strong>${modeNames[data.mode] || data.mode}</p>
                </div>

                <div style="margin-bottom: 20px;">
                    <h5 style="color: #9b59b6; margin-bottom: 10px;">🤖 AI智能总结</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #9b59b6; white-space: pre-wrap;">
                        ${correctedAISummary.replace(/\n/g, '<br>')}
                    </div>
                </div>

                ${data.transcription ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #27ae60; margin-bottom: 10px;">🎤 语音转录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #27ae60;">
                        ${data.transcription.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}

                ${data.manualNotes ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #e74c3c; margin-bottom: 10px;">✏️ 手动记录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #e74c3c;">
                        ${data.manualNotes.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}

                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 12px;">
                    📝 纪要生成时间：${new Date().toLocaleString('zh-CN')} | 🤖 AI增强处理
                </div>
            </div>
        `;

        // 生成后显示纠错工具栏
        setTimeout(() => {
            const toolbar = document.querySelector('.correction-toolbar');
            if (toolbar) {
                toolbar.style.display = 'flex';
            }
        }, 100);

        return summary;
    }

    // 创建会议纪要HTML
    createMeetingSummary(data) {
        const formatTime = (timeString) => {
            if (!timeString) return '未设置';
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN');
        };
        
        return `
            <div class="summary-content">
                <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px;">
                    📋 ${data.title}
                </h4>
                
                <div style="margin-bottom: 20px;">
                    <p><strong>🕐 会议时间：</strong>${formatTime(data.time)}</p>
                    <p><strong>👥 参会人员：</strong>${data.participants || '未填写'}</p>
                </div>
                
                ${data.transcription ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #27ae60; margin-bottom: 10px;">🎤 语音转录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #27ae60;">
                        ${data.transcription.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}
                
                ${data.manualNotes ? `
                <div style="margin-bottom: 20px;">
                    <h5 style="color: #e74c3c; margin-bottom: 10px;">✏️ 手动记录内容</h5>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #e74c3c;">
                        ${data.manualNotes.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}
                
                <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 12px;">
                    📝 纪要生成时间：${new Date().toLocaleString('zh-CN')}
                </div>
            </div>
        `;
    }

    // 保存会议记录
    saveMeeting() {
        const meetingData = {
            id: Date.now(),
            title: document.getElementById('meetingTitle').value || '未命名会议',
            time: document.getElementById('meetingTime').value,
            participants: document.getElementById('participants').value,
            transcription: this.transcriptionText,
            manualNotes: document.getElementById('manualNotes').value,
            summary: document.getElementById('meetingSummary').innerHTML,
            savedAt: new Date().toISOString()
        };
        
        if (!meetingData.transcription.trim() && !meetingData.manualNotes.trim()) {
            this.showModal('没有内容可保存，请先录制语音或输入笔记。');
            return;
        }
        
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        savedMeetings.unshift(meetingData);
        
        // 只保留最近50条记录
        if (savedMeetings.length > 50) {
            savedMeetings.splice(50);
        }
        
        localStorage.setItem('meetingNotes', JSON.stringify(savedMeetings));
        this.showModal('会议记录已保存成功！');
        this.loadSavedMeetings();
    }

    // 导出会议记录
    exportMeeting() {
        const summary = document.getElementById('meetingSummary').innerHTML;
        if (!summary || summary.includes('placeholder-text')) {
            this.showModal('请先生成会议纪要后再导出。');
            return;
        }
        
        const meetingTitle = document.getElementById('meetingTitle').value || '会议记录';
        const exportContent = this.createExportContent();
        
        const blob = new Blob([exportContent], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${meetingTitle}_${new Date().toISOString().slice(0, 10)}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showModal('会议记录已导出成功！');
    }

    // 创建导出内容
    createExportContent() {
        const summary = document.getElementById('meetingSummary').innerHTML;
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议记录 - Jason's Meeting Note</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; line-height: 1.6; margin: 40px; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { max-width: 800px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📝 Jason's Meeting Note</h1>
        <p>智能会议记录系统导出文档</p>
    </div>
    <div class="content">
        ${summary}
    </div>
</body>
</html>
        `;
    }

    // 新建会议
    newMeeting() {
        if (confirm('确定要新建会议吗？当前未保存的内容将会丢失。')) {
            document.getElementById('meetingTitle').value = '';
            document.getElementById('participants').value = '';
            this.clearTranscription();
            this.clearManualNotes();
            document.getElementById('meetingSummary').innerHTML = '<p class="placeholder-text">点击"生成纪要"按钮，系统将自动整合语音转录和手动笔记，生成结构化的会议纪要...</p>';
            this.setDefaultDateTime();
            this.stopRecording();
        }
    }

    // 加载保存的会议
    loadSavedMeetings() {
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        const historyList = document.getElementById('historyList');
        
        if (savedMeetings.length === 0) {
            historyList.innerHTML = '<p class="placeholder-text">暂无历史会议记录</p>';
            return;
        }
        
        historyList.innerHTML = savedMeetings.map(meeting => `
            <div class="history-item" style="border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 10px; cursor: pointer;" 
                 onclick="meetingApp.loadMeetingData(${meeting.id})">
                <h5 style="margin: 0 0 5px 0; color: #2c3e50;">${meeting.title}</h5>
                <p style="margin: 0; color: #6c757d; font-size: 12px;">
                    保存时间: ${new Date(meeting.savedAt).toLocaleString('zh-CN')}
                </p>
            </div>
        `).join('');
    }

    // 加载特定会议数据
    loadMeetingData(meetingId) {
        const savedMeetings = JSON.parse(localStorage.getItem('meetingNotes') || '[]');
        const meeting = savedMeetings.find(m => m.id === meetingId);
        
        if (meeting) {
            document.getElementById('meetingTitle').value = meeting.title;
            document.getElementById('meetingTime').value = meeting.time;
            document.getElementById('participants').value = meeting.participants;
            this.transcriptionText = meeting.transcription;
            document.getElementById('manualNotes').value = meeting.manualNotes;
            
            this.updateTranscriptionDisplay(meeting.transcription, '');
            if (meeting.summary) {
                document.getElementById('meetingSummary').innerHTML = meeting.summary;
            }
            
            this.toggleHistorySection();
            this.showModal('会议记录已加载成功！');
        }
    }

    // 切换历史记录显示
    toggleHistorySection() {
        const historySection = document.querySelector('.history-section');
        if (historySection.style.display === 'none' || !historySection.style.display) {
            historySection.style.display = 'block';
            this.loadSavedMeetings();
        } else {
            historySection.style.display = 'none';
        }
    }

    // 显示模态框
    showModal(message) {
        document.getElementById('modalMessage').innerHTML = message;
        document.getElementById('statusModal').style.display = 'block';
    }

    // 关闭模态框
    closeModal() {
        document.getElementById('statusModal').style.display = 'none';
    }

    // 词典管理相关方法

    // 设置词典管理事件监听器
    setupDictionaryEventListeners() {
        // 词典模态框相关
        const closeDictionary = document.getElementById('closeDictionary');
        if (closeDictionary) {
            closeDictionary.addEventListener('click', () => this.closeDictionaryModal());
        }

        // 标签页切换
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const tabName = e.target.dataset.tab;
                this.switchDictionaryTab(tabName);
            });
        });

        // 快速纠错模态框
        const closeQuickCorrection = document.getElementById('closeQuickCorrection');
        if (closeQuickCorrection) {
            closeQuickCorrection.addEventListener('click', () => this.closeQuickCorrectionModal());
        }

        const saveCorrection = document.getElementById('saveCorrection');
        if (saveCorrection) {
            saveCorrection.addEventListener('click', () => this.saveQuickCorrection());
        }

        const cancelCorrection = document.getElementById('cancelCorrection');
        if (cancelCorrection) {
            cancelCorrection.addEventListener('click', () => this.closeQuickCorrectionModal());
        }

        // 词典操作按钮
        const exportDictionary = document.getElementById('exportDictionary');
        if (exportDictionary) {
            exportDictionary.addEventListener('click', () => this.exportDictionary());
        }

        const importDictionary = document.getElementById('importDictionary');
        if (importDictionary) {
            importDictionary.addEventListener('click', () => this.importDictionary());
        }

        const clearDictionary = document.getElementById('clearDictionary');
        if (clearDictionary) {
            clearDictionary.addEventListener('click', () => this.clearDictionary());
        }

        // 搜索功能
        const searchInput = document.getElementById('searchDictionary');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.searchDictionary(e.target.value));
        }

        // 设置相关
        const enableSmartCorrection = document.getElementById('enableSmartCorrection');
        if (enableSmartCorrection) {
            enableSmartCorrection.addEventListener('change', (e) => {
                this.smartCorrector.setEnabled(e.target.checked);
                this.saveCorrectionSettings();
            });
        }

        const matchThreshold = document.getElementById('matchThreshold');
        const thresholdValue = document.getElementById('thresholdValue');
        if (matchThreshold && thresholdValue) {
            matchThreshold.addEventListener('input', (e) => {
                thresholdValue.textContent = e.target.value;
                this.saveCorrectionSettings();
            });
        }
    }

    // 显示词典管理模态框
    showDictionaryModal() {
        const modal = document.getElementById('dictionaryModal');
        modal.style.display = 'block';
        this.loadDictionaryContent();
        this.updateDictionaryStatistics();
    }

    // 关闭词典管理模态框
    closeDictionaryModal() {
        const modal = document.getElementById('dictionaryModal');
        modal.style.display = 'none';
    }

    // 关闭快速纠错模态框
    closeQuickCorrectionModal() {
        const modal = document.getElementById('quickCorrectionModal');
        modal.style.display = 'none';
        this.currentEditingElement = null;
    }

    // 切换词典标签页
    switchDictionaryTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新标签页内容
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');

        // 根据标签页加载相应内容
        switch (tabName) {
            case 'learned':
                this.loadDictionaryContent();
                break;
            case 'statistics':
                this.updateDictionaryStatistics();
                break;
            case 'settings':
                this.loadCorrectionSettings();
                break;
        }
    }

    // 加载词典内容
    loadDictionaryContent() {
        const dictionaryList = document.getElementById('dictionaryList');
        const corrections = this.correctionDictionary.getAllCorrections();

        if (corrections.length === 0) {
            dictionaryList.innerHTML = '<p class="placeholder-text">暂无学习的词汇</p>';
            return;
        }

        dictionaryList.innerHTML = corrections.map(correction => `
            <div class="dictionary-item">
                <div class="word-info">
                    <div class="word-mapping">
                        <span class="word-wrong">${correction.wrong}</span>
                        <span>→</span>
                        <span class="word-correct">${correction.correct}</span>
                    </div>
                    <div class="word-meta">
                        使用次数: ${correction.frequency} |
                        创建时间: ${new Date(correction.createdAt).toLocaleDateString()}
                    </div>
                </div>
                <div class="word-actions">
                    <button class="btn btn-sm btn-outline" onclick="meetingApp.editDictionaryItem('${correction.wrong}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="meetingApp.removeDictionaryItem('${correction.wrong}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 更新词典统计信息
    updateDictionaryStatistics() {
        const stats = this.correctionDictionary.getStatistics();

        document.getElementById('totalCorrections').textContent = stats.totalCorrections;
        document.getElementById('totalWords').textContent = stats.wordsLearned;
        document.getElementById('lastUpdate').textContent = stats.lastUpdated ?
            new Date(stats.lastUpdated).toLocaleDateString() : '-';
    }

    // 更新纠错统计显示
    updateCorrectionStats() {
        const stats = this.correctionDictionary.getStatistics();
        const learnedCount = document.getElementById('learnedCount');
        if (learnedCount) {
            learnedCount.textContent = stats.wordsLearned;
        }
    }

    // 搜索词典
    searchDictionary(query) {
        const items = document.querySelectorAll('.dictionary-item');
        const searchTerm = query.toLowerCase();

        items.forEach(item => {
            const text = item.textContent.toLowerCase();
            item.style.display = text.includes(searchTerm) ? 'flex' : 'none';
        });
    }
}

    // 保存快速纠错
    saveQuickCorrection() {
        const wrongWord = document.getElementById('wrongWord').value.trim();
        const correctWord = document.getElementById('correctWord').value.trim();
        const context = document.getElementById('correctionContext').value.trim();

        if (!correctWord) {
            this.showModal('请输入正确的词汇');
            return;
        }

        // 保存到词典
        this.correctionDictionary.addCorrection(wrongWord, correctWord, context);

        // 更新当前编辑的元素
        if (this.currentEditingElement) {
            this.currentEditingElement.textContent = correctWord;
            this.currentEditingElement.classList.add('corrected');
        }

        this.updateCorrectionStats();
        this.closeQuickCorrectionModal();
        this.showModal(`已学习纠错: "${wrongWord}" → "${correctWord}"`);
    }

    // 编辑词典项
    editDictionaryItem(wrongWord) {
        const correction = this.correctionDictionary.corrections[wrongWord.toLowerCase()];
        if (correction) {
            document.getElementById('wrongWord').value = wrongWord;
            document.getElementById('correctWord').value = correction.correct;
            document.getElementById('correctionContext').value = correction.contexts.join(', ');

            const modal = document.getElementById('quickCorrectionModal');
            modal.style.display = 'block';
        }
    }

    // 删除词典项
    removeDictionaryItem(wrongWord) {
        if (confirm(`确定要删除纠错规则 "${wrongWord}" 吗？`)) {
            this.correctionDictionary.removeCorrection(wrongWord);
            this.loadDictionaryContent();
            this.updateCorrectionStats();
            this.showModal('纠错规则已删除');
        }
    }

    // 导出词典
    exportDictionary() {
        const data = this.correctionDictionary.exportDictionary();
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `纠错词典_${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        this.showModal('词典已导出成功！');
    }

    // 导入词典
    importDictionary() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        if (this.correctionDictionary.importDictionary(data)) {
                            this.loadDictionaryContent();
                            this.updateCorrectionStats();
                            this.showModal('词典导入成功！');
                        } else {
                            this.showModal('词典格式不正确，导入失败');
                        }
                    } catch (error) {
                        this.showModal('文件格式错误，导入失败');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    // 清空词典
    clearDictionary() {
        if (confirm('确定要清空所有学习的词汇吗？此操作不可恢复。')) {
            this.correctionDictionary.clearDictionary();
            this.loadDictionaryContent();
            this.updateCorrectionStats();
            this.showModal('词典已清空');
        }
    }

    // 加载纠错设置
    loadCorrectionSettings() {
        const settings = JSON.parse(localStorage.getItem('correctionSettings') || '{}');

        const enableSmartCorrection = document.getElementById('enableSmartCorrection');
        const enableFuzzyMatch = document.getElementById('enableFuzzyMatch');
        const matchThreshold = document.getElementById('matchThreshold');
        const thresholdValue = document.getElementById('thresholdValue');

        if (enableSmartCorrection) {
            enableSmartCorrection.checked = settings.smartCorrectionEnabled !== false;
        }
        if (enableFuzzyMatch) {
            enableFuzzyMatch.checked = settings.fuzzyMatchEnabled !== false;
        }
        if (matchThreshold && thresholdValue) {
            const threshold = settings.matchThreshold || 0.7;
            matchThreshold.value = threshold;
            thresholdValue.textContent = threshold;
        }
    }

    // 保存纠错设置
    saveCorrectionSettings() {
        const settings = {
            smartCorrectionEnabled: document.getElementById('enableSmartCorrection')?.checked !== false,
            fuzzyMatchEnabled: document.getElementById('enableFuzzyMatch')?.checked !== false,
            matchThreshold: parseFloat(document.getElementById('matchThreshold')?.value || 0.7)
        };

        localStorage.setItem('correctionSettings', JSON.stringify(settings));

        // 应用设置
        this.smartCorrector.setEnabled(settings.smartCorrectionEnabled);
    }

    // 取消编辑模式
    cancelEditMode() {
        this.isEditMode = false;
        this.toggleEditMode();
    }

    // 应用待处理的纠错
    applyPendingCorrections() {
        const correctedWords = document.querySelectorAll('.editable-word.corrected');
        let appliedCount = 0;

        correctedWords.forEach(wordElement => {
            appliedCount++;
        });

        if (appliedCount > 0) {
            this.showModal(`已应用 ${appliedCount} 个纠错`);
        }

        this.cancelEditMode();
    }

    // 转义正则表达式特殊字符
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
}

// 初始化应用
let meetingApp;
document.addEventListener('DOMContentLoaded', () => {
    meetingApp = new MeetingNoteApp();
    window.meetingApp = meetingApp; // 将实例暴露到全局作用域
});
