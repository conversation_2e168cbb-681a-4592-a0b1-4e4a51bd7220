<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI API 测试工具 & API Key 验证器</h1>

        <div class="form-group">
            <label for="provider">选择AI提供商:</label>
            <select id="provider" onchange="updateFormatHint()">
                <option value="deepseek">DeepSeek</option>
                <option value="openai">OpenAI</option>
                <option value="anthropic">Anthropic Claude</option>
                <option value="gemini">Google Gemini</option>
            </select>
        </div>

        <div id="formatHint" style="background: #e3f2fd; padding: 10px; border-radius: 4px; margin-bottom: 15px; font-size: 14px;">
            <strong>DeepSeek API Key格式:</strong> sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (以sk-开头，后跟字母数字)
            <br><strong>示例:</strong> sk-1234567890abcdef1234567890abcdef
        </div>

        <div class="form-group">
            <label for="apiKey">API Key:</label>
            <input type="password" id="apiKey" placeholder="输入你的API Key" oninput="validateKeyFormat()">
            <button type="button" onclick="toggleKeyVisibility()" style="margin-top: 5px;">显示/隐藏</button>
        </div>

        <div id="validationResult" style="margin-bottom: 15px;"></div>

        <div class="form-group">
            <label for="testMessage">测试消息:</label>
            <textarea id="testMessage" rows="3" placeholder="输入要测试的消息">你好，请简单介绍一下你自己。</textarea>
        </div>

        <button onclick="validateApiKey()">验证API Key格式</button>
        <button onclick="testConnection()">测试连接</button>
        <button onclick="sendMessage()">发送消息</button>

        <div id="result"></div>
    </div>

    <script>
        const providers = {
            deepseek: {
                name: 'DeepSeek',
                endpoint: 'https://api.deepseek.com/v1/chat/completions',
                keyFormat: /^sk-[A-Za-z0-9]{20,}$/,
                formatHint: 'DeepSeek API Key格式: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (以sk-开头，后跟字母数字)',
                example: 'sk-1234567890abcdef1234567890abcdef',
                headers: (apiKey) => ({
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                }),
                body: (message) => ({
                    model: 'deepseek-chat',
                    messages: [{ role: 'user', content: message }],
                    max_tokens: 100
                }),
                parseResponse: (data) => data.choices[0].message.content
            },
            openai: {
                name: 'OpenAI',
                endpoint: 'https://api.openai.com/v1/chat/completions',
                keyFormat: /^sk-[A-Za-z0-9]{20,}$/,
                formatHint: 'OpenAI API Key格式: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (以sk-开头，后跟字母数字)',
                example: 'sk-1234567890abcdef1234567890abcdef1234567890abcdef',
                headers: (apiKey) => ({
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                }),
                body: (message) => ({
                    model: 'gpt-3.5-turbo',
                    messages: [{ role: 'user', content: message }],
                    max_tokens: 100
                }),
                parseResponse: (data) => data.choices[0].message.content
            },
            anthropic: {
                name: 'Anthropic',
                endpoint: 'https://api.anthropic.com/v1/messages',
                keyFormat: /^sk-ant-[A-Za-z0-9_-]{20,}$/,
                formatHint: 'Anthropic API Key格式: sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (以sk-ant-开头)',
                example: 'sk-ant-api03-1234567890abcdef1234567890abcdef',
                headers: (apiKey) => ({
                    'Content-Type': 'application/json',
                    'x-api-key': apiKey,
                    'anthropic-version': '2023-06-01'
                }),
                body: (message) => ({
                    model: 'claude-3-haiku-20240307',
                    messages: [{ role: 'user', content: message }],
                    max_tokens: 100
                }),
                parseResponse: (data) => data.content[0].text
            },
            gemini: {
                name: 'Google Gemini',
                endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
                keyFormat: /^[A-Za-z0-9_-]{20,}$/,
                formatHint: 'Google API Key格式: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (纯字母数字字符串)',
                example: 'AIzaSyDhGjCvK1234567890abcdefghijklmnop',
                headers: (apiKey) => ({
                    'Content-Type': 'application/json'
                }),
                body: (message) => ({
                    contents: [{ parts: [{ text: message }] }],
                    generationConfig: { maxOutputTokens: 100 }
                }),
                parseResponse: (data) => data.candidates[0].content.parts[0].text,
                useKeyInUrl: true
            }
        };

        function updateFormatHint() {
            const provider = document.getElementById('provider').value;
            const config = providers[provider];
            document.getElementById('formatHint').innerHTML =
                `<strong>${config.name} API Key格式:</strong> ${config.formatHint}<br><strong>示例:</strong> ${config.example}`;
        }

        function validateKeyFormat() {
            const provider = document.getElementById('provider').value;
            const apiKey = document.getElementById('apiKey').value.trim();
            const config = providers[provider];
            const resultDiv = document.getElementById('validationResult');

            if (apiKey.length < 5) {
                resultDiv.innerHTML = '';
                return;
            }

            if (config.keyFormat.test(apiKey)) {
                resultDiv.innerHTML = '<div style="color: green;">✓ API Key格式正确</div>';
            } else {
                let error = '';
                if (provider === 'deepseek' || provider === 'openai') {
                    if (!apiKey.startsWith('sk-')) {
                        error = `${config.name} API Key必须以"sk-"开头`;
                    } else {
                        error = `${config.name} API Key格式不正确，应为: sk-后跟字母数字字符`;
                    }
                } else if (provider === 'anthropic') {
                    if (!apiKey.startsWith('sk-ant-')) {
                        error = 'Anthropic API Key必须以"sk-ant-"开头';
                    } else {
                        error = 'Anthropic API Key格式不正确，应为: sk-ant-后跟字母数字字符';
                    }
                } else if (provider === 'gemini') {
                    error = 'Google Gemini API Key应为纯字母数字字符串（可包含下划线和连字符）';
                }
                resultDiv.innerHTML = `<div style="color: red;">✗ ${error}</div>`;
            }
        }

        function validateApiKey() {
            validateKeyFormat();
        }

        function toggleKeyVisibility() {
            const input = document.getElementById('apiKey');
            input.type = input.type === 'password' ? 'text' : 'password';
        }

        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.textContent = message;
        }

        async function testConnection() {
            const provider = document.getElementById('provider').value;
            const apiKey = document.getElementById('apiKey').value.trim();

            if (!apiKey) {
                showResult('请输入API Key', true);
                return;
            }

            showResult('正在测试连接...');

            try {
                await makeApiCall(provider, apiKey, '你好');
                showResult('连接成功！API Key有效。');
            } catch (error) {
                showResult(`连接失败: ${error.message}`, true);
            }
        }

        async function sendMessage() {
            const provider = document.getElementById('provider').value;
            const apiKey = document.getElementById('apiKey').value.trim();
            const message = document.getElementById('testMessage').value.trim();

            if (!apiKey) {
                showResult('请输入API Key', true);
                return;
            }

            if (!message) {
                showResult('请输入测试消息', true);
                return;
            }

            showResult('正在发送消息...');

            try {
                const response = await makeApiCall(provider, apiKey, message);
                showResult(`AI回复:\n${response}`);
            } catch (error) {
                showResult(`发送失败: ${error.message}`, true);
            }
        }

        async function makeApiCall(providerKey, apiKey, message) {
            const provider = providers[providerKey];
            let url = provider.endpoint;

            if (provider.useKeyInUrl) {
                url += `?key=${apiKey}`;
            }

            const response = await fetch(url, {
                method: 'POST',
                headers: provider.headers(apiKey),
                body: JSON.stringify(provider.body(message))
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error?.message || `HTTP ${response.status}`);
            }

            const data = await response.json();
            return provider.parseResponse(data);
        }
    </script>
</body>
</html>
