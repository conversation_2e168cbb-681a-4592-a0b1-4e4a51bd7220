<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI功能简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
        }
        .ai-settings-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #9b59b6;
        }
        .ai-settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .ai-settings-panel {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            border: 1px solid #ddd;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn-outline {
            background: transparent;
            color: #6c757d;
            border: 2px solid #e9ecef;
        }
        .btn-outline:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }
        .config-row {
            margin: 15px 0;
        }
        .config-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .config-select {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI功能简单测试</h1>
        
        <!-- AI设置区域 -->
        <section class="ai-settings-section">
            <div class="ai-settings-header">
                <h3><i class="fas fa-robot"></i> AI智能助手设置</h3>
                <button id="toggleAiSettings" class="btn btn-outline">
                    <i class="fas fa-cog"></i> 配置AI
                </button>
            </div>
            
            <div id="aiSettingsPanel" class="ai-settings-panel" style="display: none;">
                <div class="config-row">
                    <label for="apiProvider" class="config-label">选择AI服务商：</label>
                    <select id="apiProvider" class="config-select">
                        <option value="">请选择AI服务商</option>
                        <option value="deepseek">DeepSeek API</option>
                        <option value="minimax">MiniMax API</option>
                        <option value="doubao">豆包(字节跳动) API</option>
                        <option value="gemini">Google Gemini API</option>
                        <option value="openai">OpenAI GPT API</option>
                    </select>
                </div>
                
                <div class="config-row">
                    <button id="testButton" class="btn btn-outline">测试功能</button>
                </div>
            </div>
        </section>
        
        <div id="status" class="status info">
            点击"配置AI"按钮测试面板切换功能
        </div>
        
        <div style="margin-top: 20px;">
            <h3>调试信息：</h3>
            <div id="debugInfo" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap;"></div>
        </div>
    </div>

    <script>
        // 简化的测试类
        class SimpleTest {
            constructor() {
                this.setupEventListeners();
                this.log('SimpleTest 初始化完成');
            }
            
            setupEventListeners() {
                const toggleButton = document.getElementById('toggleAiSettings');
                const testButton = document.getElementById('testButton');
                
                if (toggleButton) {
                    toggleButton.addEventListener('click', () => {
                        this.log('配置AI按钮被点击');
                        this.toggleAISettings();
                    });
                    this.log('配置AI按钮事件监听器已绑定');
                } else {
                    this.log('错误: 未找到配置AI按钮', 'error');
                }
                
                if (testButton) {
                    testButton.addEventListener('click', () => {
                        this.log('测试按钮被点击');
                        this.testFunction();
                    });
                }
            }
            
            toggleAISettings() {
                const panel = document.getElementById('aiSettingsPanel');
                const button = document.getElementById('toggleAiSettings');
                
                if (!panel || !button) {
                    this.log('错误: 面板或按钮未找到', 'error');
                    return;
                }
                
                const isVisible = panel.style.display === 'block';
                panel.style.display = isVisible ? 'none' : 'block';
                
                button.innerHTML = isVisible ?
                    '<i class="fas fa-cog"></i> 配置AI' :
                    '<i class="fas fa-times"></i> 关闭配置';
                
                this.log(`面板状态切换: ${isVisible ? '关闭' : '打开'}`, 'success');
                
                const status = document.getElementById('status');
                status.textContent = isVisible ? 
                    'AI设置面板已关闭' : 
                    'AI设置面板已打开 - 功能正常！';
                status.className = 'status success';
            }
            
            testFunction() {
                const provider = document.getElementById('apiProvider').value;
                this.log(`选择的服务商: ${provider || '未选择'}`, 'info');
                
                const status = document.getElementById('status');
                status.textContent = `测试完成 - 选择的服务商: ${provider || '未选择'}`;
                status.className = 'status info';
            }
            
            log(message, type = 'info') {
                const debugInfo = document.getElementById('debugInfo');
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `[${timestamp}] ${message}\n`;
                
                debugInfo.textContent += logEntry;
                debugInfo.scrollTop = debugInfo.scrollHeight;
                
                console.log(`[SimpleTest] ${message}`);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            window.simpleTest = new SimpleTest();
        });
    </script>
    
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</body>
</html>
