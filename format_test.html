<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>格式化测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', system-ui, sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">新格式化效果测试</h1>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 输入区 -->
            <div class="bg-white rounded-xl shadow-sm p-5 border border-gray-100">
                <h2 class="text-lg font-semibold mb-4">测试内容输入</h2>
                <textarea id="testInput" class="w-full h-40 p-3 border border-gray-200 rounded-lg" placeholder="输入测试内容...">滴滴Kwaipilot拜访交流

对接团队情况
对接团队为研发效能团队，40-50开发人员规模，工作主要涉及内部研发提效工具平台的建设，冯上为团队负责人

AI Coding现状
目前滴滴有自研IDE插件，内部采纳率为20%出头，关注定制模型相较开源模型带来的提升对比情况，反馈1年内暂无人力投入训练模型&微调优化，会考虑采购外部厂商能力，但需沿用自有插件

友商对接情况
24年与阿里、百度沟通过，但对方反馈倾向输出标品方案，不希望做模型定制化调优，导致项目搁置，25年暂未做过正式交流推进

POC预期推进计划
代码续写+RAG，一期会覆盖50人左右，A100+L20共4张，若采纳率提升达到5%-10%，则考虑扩大POC范围至200人左右，最终采纳率提升大于10%会考虑采购。端午节后客户用2周左右时间完成接口协议+插件改造说明+GPU资源申请</textarea>
                <button onclick="testFormat()" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    测试格式化
                </button>
            </div>

            <!-- 输出区 -->
            <div class="bg-white rounded-xl shadow-sm p-5 border border-gray-100">
                <h2 class="text-lg font-semibold mb-4">格式化结果</h2>
                <div id="testOutput" class="prose max-w-none">
                    <!-- 格式化结果将显示在这里 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 阿拉伯数字格式化函数（使用中文顿号）
        const getNumberWithComma = (num) => {
            return `${num}、`; // 使用阿拉伯数字 + 中文顿号
        };

        // 格式化函数（新的商务报告格式）
        const formatContent = (content) => {
            const lines = content.split('\n').filter(line => line.trim());
            const processedLines = [];
            let itemCounter = 0;

            // 智能分组和合并：将相关内容合并为完整段落
            const consolidatedItems = [];
            let currentItem = { content: '', relatedLines: [] };

            for (let line of lines) {
                // 清理行内容，移除各种序号和标记
                line = line.replace(/^\s*(?:[0-9一二三四五六七八九十]+[\.。、）\)：:]+\s*|[•\-\*]\s+|[（\(][0-9一二三四五六七八九十]+[）\)]\s*)/g, '').trim();

                // 检查是否是分类标题（但不作为标题显示，而是作为内容的一部分）
                const categoryTitles = ["客户业务场景", "客户关注点", "客户痛点", "竞品情况", "技术方案", "项目进展", "工作内容", "完成情况", "对接团队情况", "现状分析", "友商对接情况", "POC预期推进计划"];
                const titleRegex = new RegExp(`^(${categoryTitles.join('|')})[:：]?`, 'i');
                const titleMatch = line.match(titleRegex);

                if (titleMatch) {
                    // 如果之前有内容，先保存
                    if (currentItem.content || currentItem.relatedLines.length > 0) {
                        consolidatedItems.push(currentItem);
                    }

                    // 开始新的条目，将分类标题作为内容的开头
                    currentItem = {
                        content: titleMatch[1],
                        relatedLines: []
                    };

                    // 移除标题部分，保留后续内容
                    line = line.replace(titleRegex, '').trim();
                    if (line) {
                        currentItem.relatedLines.push(line);
                    }
                } else if (line) {
                    // 检查是否是新的主要内容点（通常较长且包含关键信息）
                    if (line.length > 20 && (line.includes('，') || line.includes('；') || line.includes('、'))) {
                        // 如果之前有内容，先保存
                        if (currentItem.content || currentItem.relatedLines.length > 0) {
                            consolidatedItems.push(currentItem);
                        }

                        // 开始新的条目
                        currentItem = {
                            content: line,
                            relatedLines: []
                        };
                    } else {
                        // 作为相关信息添加到当前条目
                        currentItem.relatedLines.push(line);
                    }
                }
            }

            // 添加最后一个条目
            if (currentItem.content || currentItem.relatedLines.length > 0) {
                consolidatedItems.push(currentItem);
            }

            // 智能合并和优化：如果条目超过4个，进行合并
            const MAX_ITEMS = 4;
            let finalItems = [...consolidatedItems];

            if (finalItems.length > MAX_ITEMS) {
                // 按优先级排序，优先保留重要内容
                finalItems.sort((a, b) => (a.priority || 3) - (b.priority || 3));

                // 保留前3个最重要的条目，将剩余的合并为第4个条目
                const keepItems = finalItems.slice(0, 3);
                const mergeItems = finalItems.slice(3);

                if (mergeItems.length > 0) {
                    // 创建合并条目
                    const mergedContent = mergeItems.map(item => {
                        let content = item.content;
                        if (item.relatedLines.length > 0) {
                            content += '，' + item.relatedLines.join('，');
                        }
                        return content;
                    }).join('；');

                    keepItems.push({
                        content: '其他工作内容',
                        relatedLines: [mergedContent],
                        priority: 4
                    });
                }

                finalItems = keepItems;
            }

            // 生成最终的段落描述（限制4条）
            finalItems.forEach(item => {
                if (item.content || item.relatedLines.length > 0) {
                    itemCounter++;

                    // 合并主要内容和相关信息为完整段落
                    let fullContent = item.content;
                    if (item.relatedLines.length > 0) {
                        // 智能连接相关信息
                        const relatedText = item.relatedLines.join('，');
                        if (fullContent) {
                            // 如果主要内容不以标点符号结尾，添加连接符
                            if (!fullContent.match(/[，。；：、]$/)) {
                                fullContent += '，';
                            }
                            fullContent += relatedText;
                        } else {
                            fullContent = relatedText;
                        }
                    }

                    // 确保段落以分号结尾（商务报告风格）
                    if (fullContent && !fullContent.match(/[；。]$/)) {
                        fullContent += '；';
                    }

                    // 生成格式化的条目
                    processedLines.push(`
                        <div class="mb-4 leading-relaxed">
                            <span class="font-semibold text-blue-600 mr-2">${getNumberWithComma(itemCounter)}</span>
                            <span class="text-gray-800">${fullContent}</span>
                        </div>
                    `);
                }
            });

            // 如果没有任何内容，处理原始行
            if (consolidatedItems.length === 0 && lines.length > 0) {
                lines.forEach((line, index) => {
                    line = line.replace(/^\s*(?:[0-9一二三四五六七八九十]+[\.。、）\)：:]+\s*|[•\-\*]\s+|[（\(][0-9一二三四五六七八九十]+[）\)]\s*)/g, '').trim();
                    if (line) {
                        if (!line.match(/[；。]$/)) {
                            line += '；';
                        }
                        processedLines.push(`
                            <div class="mb-4 leading-relaxed">
                                <span class="font-semibold text-blue-600 mr-2">${getNumberWithComma(index + 1)}</span>
                                <span class="text-gray-800">${line}</span>
                            </div>
                        `);
                    }
                });
            }

            return processedLines.join('');
        };

        function testFormat() {
            const input = document.getElementById('testInput').value;
            const output = document.getElementById('testOutput');

            if (!input.trim()) {
                output.innerHTML = '<p class="text-gray-500">请输入测试内容</p>';
                return;
            }

            const formatted = formatContent(input);
            output.innerHTML = formatted;
        }

        // 页面加载时自动测试
        window.onload = () => {
            testFormat();
        };
    </script>
</body>
</html>
