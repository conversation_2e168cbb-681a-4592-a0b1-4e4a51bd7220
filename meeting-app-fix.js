// 创建一个全新的简化脚本文件，专注于修复基本功能
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 修复脚本已加载');
    
    // 直接绑定事件，不依赖于复杂的类结构
    setupEventListeners();
    
    // 显示成功消息
    showStatusMessage('应用已成功加载，按钮功能已修复');
});

// 设置所有事件监听器
function setupEventListeners() {
    console.log('🔄 正在设置事件监听器...');
    
    // AI设置面板切换
    bindClickEvent('toggleAISettings', function() {
        console.log('AI设置按钮被点击');
        const panel = document.getElementById('aiSettingsPanel');
        if (panel) {
            if (panel.style.display === 'none' || !panel.style.display) {
                panel.style.display = 'block';
                this.innerHTML = '<i class="fas fa-chevron-up"></i> 收起设置';
            } else {
                panel.style.display = 'none';
                this.innerHTML = '<i class="fas fa-cog"></i> 配置AI';
            }
        }
    });
    
    // AI服务商选择
    bindChangeEvent('aiProvider', function() {
        console.log('AI服务商已选择:', this.value);
        const configSection = document.getElementById('apiConfigSection');
        if (configSection) {
            configSection.style.display = this.value ? 'block' : 'none';
        }
    });
    
    // 录音控制
    bindClickEvent('startRecording', function() {
        console.log('开始录音按钮被点击');
        this.disabled = true;
        
        const stopBtn = document.getElementById('stopRecording');
        const pauseBtn = document.getElementById('pauseRecording');
        const status = document.getElementById('recordingStatus');
        
        if (stopBtn) stopBtn.disabled = false;
        if (pauseBtn) pauseBtn.disabled = false;
        if (status) status.textContent = '正在录音...';
        
        showStatusMessage('录音功能已启动');
    });
    
    bindClickEvent('stopRecording', function() {
        console.log('停止录音按钮被点击');
        this.disabled = true;
        
        const startBtn = document.getElementById('startRecording');
        const pauseBtn = document.getElementById('pauseRecording');
        const status = document.getElementById('recordingStatus');
        
        if (startBtn) startBtn.disabled = false;
        if (pauseBtn) pauseBtn.disabled = true;
        if (status) status.textContent = '录音已停止';
        
        showStatusMessage('录音已停止');
    });
    
    bindClickEvent('pauseRecording', function() {
        console.log('暂停录音按钮被点击');
        const status = document.getElementById('recordingStatus');
        
        if (this.innerHTML.includes('暂停')) {
            this.innerHTML = '<i class="fas fa-play"></i> 继续';
            if (status) status.textContent = '录音已暂停';
        } else {
            this.innerHTML = '<i class="fas fa-pause"></i> 暂停';
            if (status) status.textContent = '正在录音...';
        }
    });
    
    // 生成纪要
    bindClickEvent('generateSummary', function() {
        console.log('生成纪要按钮被点击');
        const summaryMode = document.getElementById('summaryMode');
        const mode = summaryMode ? summaryMode.value : 'basic';
        
        const aiStatus = document.getElementById('aiProcessingStatus');
        const summary = document.getElementById('meetingSummary');
        
        if (aiStatus) aiStatus.style.display = 'block';
        
        // 模拟处理
        setTimeout(function() {
            if (summary) {
                summary.innerHTML = `<h3>会议纪要 (${mode}模式)</h3><p>这是一个示例会议纪要。在实际应用中，这里会显示基于转录内容生成的会议纪要。</p>`;
            }
            if (aiStatus) aiStatus.style.display = 'none';
        }, 2000);
        
        showStatusMessage('纪要生成功能已触发');
    });
    
    // 保存、导出等功能
    bindClickEvent('saveMeeting', function() {
        showStatusMessage('会议记录已保存');
    });
    
    bindClickEvent('exportMeeting', function() {
        showStatusMessage('会议记录已导出');
    });
    
    bindClickEvent('newMeeting', function() {
        if (confirm('确定要创建新会议吗？当前内容将被清空。')) {
            document.getElementById('meetingTitle').value = '';
            document.getElementById('participants').value = '';
            document.getElementById('transcriptionText').innerHTML = '<p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>';
            document.getElementById('manualNotes').value = '';
            document.getElementById('meetingSummary').innerHTML = '<p class="placeholder-text">选择生成模式并点击"生成纪要"按钮，系统将自动整合语音转录和手动笔记，生成结构化的会议纪要...</p>';
            showStatusMessage('已创建新会议');
        }
    });
    
    // 模态框关闭
    const closeBtn = document.querySelector('.close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            const modal = document.getElementById('statusModal');
            if (modal) modal.style.display = 'none';
        });
    }
    
    console.log('✅ 所有事件监听器设置完成');
}

// 辅助函数：绑定点击事件
function bindClickEvent(elementId, handler) {
    const element = document.getElementById(elementId);
    if (element) {
        // 移除所有现有的点击事件监听器
        const newElement = element.cloneNode(true);
        element.parentNode.replaceChild(newElement, element);
        
        // 添加新的事件监听器
        newElement.addEventListener('click', handler);
        console.log(`✅ ${elementId} 点击事件已绑定`);
    } else {
        console.warn(`⚠️ 未找到元素: ${elementId}`);
    }
}

// 辅助函数：绑定变更事件
function bindChangeEvent(elementId, handler) {
    const element = document.getElementById(elementId);
    if (element) {
        // 移除所有现有的变更事件监听器
        const newElement = element.cloneNode(true);
        element.parentNode.replaceChild(newElement, element);
        
        // 添加新的事件监听器
        newElement.addEventListener('change', handler);
        console.log(`✅ ${elementId} 变更事件已绑定`);
    } else {
        console.warn(`⚠️ 未找到元素: ${elementId}`);
    }
}

// 显示状态消息
function showStatusMessage(message) {
    console.log('📢 ' + message);
    
    const modalMessage = document.getElementById('modalMessage');
    const modal = document.getElementById('statusModal');
    
    if (modalMessage && modal) {
        modalMessage.textContent = message;
        modal.style.display = 'block';
        
        // 3秒后自动关闭
        setTimeout(function() {
            modal.style.display = 'none';
        }, 3000);
    }
}