# Jason's Meeting Note - 系统音频捕获使用指南

## 功能概述

Jason's Meeting Note 现已支持多种音频捕获方式，可以有效解决腾讯会议等视频会议软件的语音转录问题。

## 音频源选择

### 1. 麦克风输入（默认）
- **适用场景**：个人语音记录、面对面会议
- **优点**：识别准确度高、延迟低
- **缺点**：无法捕获其他应用的音频

### 2. 系统音频
- **适用场景**：腾讯会议、Zoom、Teams等视频会议
- **优点**：可以捕获其他应用的音频输出
- **缺点**：需要屏幕共享权限、识别准确度可能较低

### 3. 混合音频
- **适用场景**：需要同时记录自己和他人发言的会议
- **优点**：完整记录所有音频内容
- **缺点**：资源消耗较大、设置复杂

## 使用步骤

### 捕获腾讯会议音频

1. **打开Jason's Meeting Note应用**
2. **选择音频源**：
   - 点击"系统音频"选项
   - 或选择"混合音频"（如果需要同时录制自己的声音）
3. **开始录音**：
   - 点击"开始录音"按钮
   - 浏览器会弹出屏幕共享权限请求
4. **配置屏幕共享**：
   - 选择要共享的窗口（可以选择腾讯会议窗口或整个屏幕）
   - **重要**：确保勾选"共享音频"或"共享系统音频"选项
   - 点击"共享"
5. **开始转录**：
   - 系统开始捕获音频并尝试转录
   - 转录内容会实时显示在转录框中

### 提高转录效果的技巧

1. **音频质量**：
   - 确保会议音频清晰
   - 调整系统音量到适中水平
   - 减少背景噪音

2. **浏览器选择**：
   - 推荐使用Chrome或Edge浏览器
   - 确保浏览器版本较新

3. **权限设置**：
   - 允许浏览器访问麦克风和屏幕共享
   - 在系统设置中确认音频权限

## 故障排除

### 无法捕获系统音频

**可能原因**：
- 浏览器不支持getDisplayMedia API
- 未正确配置屏幕共享
- 系统音频权限被拒绝

**解决方案**：
1. 检查浏览器版本和兼容性
2. 重新配置屏幕共享，确保选择"共享音频"
3. 检查系统音频权限设置

### 转录效果不佳

**可能原因**：
- 音频质量较差
- 语音识别API限制
- 网络连接问题

**解决方案**：
1. 提高音频质量
2. 结合手动笔记功能
3. 使用AI智能纪要生成功能

### macOS系统特殊设置

如果系统音频捕获不工作，可以尝试：

1. **安装虚拟音频设备**：
   - 下载BlackHole或SoundFlower
   - 安装后在"音频MIDI设置"中创建多输出设备
   - 将会议应用音频输出到虚拟设备

2. **系统权限设置**：
   - 系统偏好设置 → 安全性与隐私 → 隐私
   - 确保浏览器有麦克风和屏幕录制权限

### Windows系统特殊设置

1. **启用立体声混音**：
   - 右键音量图标 → 声音设置
   - 选择"声音控制面板"
   - 在录制选项卡中启用"立体声混音"

2. **音频驱动更新**：
   - 确保音频驱动程序是最新版本

## 最佳实践

1. **混合使用**：
   - 系统音频捕获 + 手动笔记
   - 语音转录 + AI智能整理

2. **备份策略**：
   - 重要会议建议同时使用多种记录方式
   - 定期保存会议记录

3. **隐私保护**：
   - 注意会议录音的隐私政策
   - 获得参会者同意后再进行录音

## 技术限制说明

1. **浏览器限制**：
   - 系统音频捕获依赖浏览器的屏幕共享API
   - 不同浏览器的支持程度可能不同

2. **识别准确度**：
   - 系统音频的语音识别准确度可能低于直接麦克风输入
   - 建议结合手动笔记使用

3. **性能影响**：
   - 混合音频模式会消耗更多系统资源
   - 长时间录制可能影响系统性能

## 联系支持

如果遇到技术问题，请：
1. 查看浏览器控制台错误信息
2. 检查系统音频权限设置
3. 尝试重启浏览器或应用

---

**注意**：此功能仍在持续优化中，如有问题或建议，欢迎反馈。
