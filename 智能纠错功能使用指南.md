# Jason's Meeting Note - 智能纠错功能使用指南

## 🎯 功能概述

智能纠错和学习功能是Jason's Meeting Note的核心增强功能，旨在解决语音识别和AI生成内容中的专有名词错误问题。通过机器学习的方式，系统能够记住用户的纠错习惯，并在后续使用中自动应用这些纠错规则。

## 🚀 核心功能

### 1. **智能纠错词典**
- **本地存储**：所有纠错规则保存在浏览器本地，保护隐私
- **自动学习**：记录用户的每次纠错操作，建立错误→正确的映射关系
- **频次统计**：跟踪纠错使用频率，优化匹配算法
- **上下文记录**：保存纠错时的上下文信息，提高准确性

### 2. **模糊匹配算法**
- **相似度计算**：使用Levenshtein距离算法计算字符串相似度
- **发音匹配**：识别同音词和近音词
- **可调阈值**：用户可自定义匹配敏感度（0.5-0.9）
- **智能建议**：主动检测可能的错误并提供纠正建议

### 3. **可编辑会议纪要**
- **纠错模式**：一键切换到编辑模式
- **点击纠错**：直接点击错误词汇进行快速修正
- **批量应用**：一次性应用多个纠错
- **实时预览**：即时查看纠错效果

### 4. **AI集成优化**
- **预处理**：发送给AI前自动应用已学习的纠错规则
- **后处理**：AI返回结果后再次应用纠错词典
- **双重保障**：确保最终输出的准确性

## 📋 使用步骤

### 步骤1：生成会议纪要
1. 录制语音或输入手动笔记
2. 选择生成模式（基础整合或AI模式）
3. 点击"生成纪要"按钮

### 步骤2：启用纠错模式
1. 纪要生成后，纠错工具栏会自动显示
2. 点击"纠错模式"按钮进入编辑状态
3. 会议纪要内容变为可编辑状态

### 步骤3：纠正错误词汇
1. 点击需要纠正的词汇
2. 在弹出的快速纠错窗口中输入正确词汇
3. 可选择添加上下文信息
4. 点击"保存纠错"完成学习

### 步骤4：应用纠错
1. 完成所有纠错后，点击"应用纠错"
2. 系统自动保存纠错规则到本地词典
3. 退出纠错模式，查看最终结果

## 🛠️ 词典管理

### 查看已学习词汇
1. 点击"词典管理"按钮
2. 在"已学习词汇"标签页查看所有纠错规则
3. 可以搜索、编辑或删除特定词汇

### 统计信息
- **总纠错次数**：累计纠错操作数量
- **已学习词汇**：词典中的词汇总数
- **最后更新**：词典最近更新时间

### 设置选项
- **启用智能纠错**：控制是否自动应用纠错规则
- **启用模糊匹配**：控制是否使用相似度匹配
- **匹配阈值**：调整模糊匹配的敏感度

### 导入导出
- **导出词典**：将词典保存为JSON文件
- **导入词典**：从文件恢复词典数据
- **清空词典**：重置所有学习数据

## 💡 使用技巧

### 1. **专有名词纠错**
```
错误：阿尔兹海默病
正确：阿尔茨海默病
上下文：医疗会议讨论
```

### 2. **人名纠错**
```
错误：张三丰
正确：张三峰
上下文：项目团队成员
```

### 3. **公司名称纠错**
```
错误：微软公司
正确：Microsoft
上下文：技术合作讨论
```

### 4. **技术术语纠错**
```
错误：人工智能
正确：AI
上下文：技术方案讨论
```

## 🎯 应用场景

### 医疗会议
- **药物名称**：复杂的药物名称经常被误识别
- **疾病术语**：专业医学术语的准确性至关重要
- **医生姓名**：确保医护人员姓名的正确记录

### 技术会议
- **技术术语**：编程语言、框架名称等
- **产品名称**：软件、硬件产品的准确命名
- **公司名称**：合作伙伴、客户公司名称

### 商务会议
- **人员姓名**：客户、合作伙伴姓名
- **地名**：城市、国家、地区名称
- **品牌名称**：产品、服务品牌

### 学术会议
- **学术术语**：专业领域的术语
- **研究机构**：大学、实验室名称
- **学者姓名**：研究人员、教授姓名

## ⚙️ 高级设置

### 匹配阈值调整
- **0.5-0.6**：宽松匹配，可能产生误匹配
- **0.7-0.8**：推荐设置，平衡准确性和覆盖率
- **0.8-0.9**：严格匹配，只匹配高度相似的词汇

### 性能优化
- **词典大小**：建议保持在1000个词汇以内
- **定期清理**：删除不再使用的纠错规则
- **备份词典**：定期导出词典进行备份

## 🔧 故障排除

### 常见问题

**Q: 纠错功能不工作？**
A: 
1. 检查"设置"中是否启用了智能纠错
2. 确认词典中有相关的纠错规则
3. 检查匹配阈值设置是否合适

**Q: 模糊匹配效果不好？**
A:
1. 调整匹配阈值到合适的值
2. 确保启用了模糊匹配功能
3. 检查词汇是否足够相似

**Q: 词典数据丢失？**
A:
1. 检查浏览器是否清除了本地存储
2. 尝试从备份文件导入词典
3. 重新学习常用的纠错规则

**Q: 纠错建议不准确？**
A:
1. 提供更详细的上下文信息
2. 删除不准确的纠错规则
3. 调整匹配阈值设置

## 📊 效果评估

### 准确性指标
- **纠错成功率**：正确应用纠错规则的比例
- **误匹配率**：错误应用纠错规则的比例
- **覆盖率**：能够识别的错误词汇比例

### 效率提升
- **纠错时间**：从发现错误到完成纠正的时间
- **学习效果**：重复错误的减少程度
- **用户满意度**：纠错功能的实用性评价

## 🔮 未来发展

### 计划功能
- **云端同步**：跨设备共享纠错词典
- **团队共享**：团队成员共享专业词典
- **AI增强**：使用AI优化纠错建议
- **语音训练**：个性化语音识别模型

### 技术改进
- **更快的匹配算法**：提高大词典的查询速度
- **更智能的建议**：基于上下文的智能推荐
- **更好的用户界面**：简化纠错操作流程

---

**开始使用智能纠错功能，让您的会议记录更加准确专业！** 🎉

通过持续学习和优化，Jason's Meeting Note将成为您最可靠的会议记录助手。
