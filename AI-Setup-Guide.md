# Jason's Meeting Note - AI功能配置指南

## 🚀 快速开始

### 1. 选择AI服务商

推荐按以下优先级选择：

#### 🥇 首选：DeepSeek API
- **优势**: 性价比最高，中文支持优秀
- **价格**: 极低成本，适合个人用户
- **注册**: https://platform.deepseek.com/
- **模型推荐**: `deepseek-chat`

#### 🥈 备选：豆包API (字节跳动)
- **优势**: 国内服务，响应速度快
- **价格**: 合理定价
- **注册**: https://console.volcengine.com/ark
- **模型推荐**: 选择最新的端点模型

#### 🥉 国际选择：OpenAI GPT
- **优势**: 技术领先，功能强大
- **价格**: 相对较高
- **注册**: https://platform.openai.com/
- **模型推荐**: `gpt-3.5-turbo` 或 `gpt-4`

### 2. 获取API Key步骤

#### DeepSeek API
1. 访问 https://platform.deepseek.com/
2. 注册账号并完成实名认证
3. 进入"API Keys"页面
4. 点击"Create API Key"
5. 复制生成的API Key

#### 豆包API
1. 访问 https://console.volcengine.com/ark
2. 注册火山引擎账号
3. 开通"豆包大模型"服务
4. 创建推理接入点
5. 获取API Key和端点ID

#### OpenAI API
1. 访问 https://platform.openai.com/
2. 注册OpenAI账号
3. 进入"API Keys"页面
4. 点击"Create new secret key"
5. 复制API Key并充值账户

### 3. 应用内配置步骤

1. **打开AI设置**
   - 点击"配置AI"按钮
   - 展开AI设置面板

2. **选择服务商**
   - 从下拉菜单选择您的AI服务商
   - 系统会自动填充默认配置

3. **输入API Key**
   - 在API Key输入框中粘贴您的密钥
   - 点击眼睛图标可切换显示/隐藏

4. **选择模型**
   - 从模型下拉菜单选择合适的模型
   - 推荐使用默认推荐的模型

5. **测试连接**
   - 点击"测试连接"按钮
   - 等待连接状态变为绿色"连接成功"

6. **保存配置**
   - 点击"保存配置"按钮
   - 配置将安全保存在本地

### 4. 高级参数说明

#### Temperature (温度)
- **范围**: 0-2
- **推荐值**: 0.7
- **说明**: 控制AI回复的创造性
  - 0: 最保守，回复固定
  - 1: 平衡创造性和准确性
  - 2: 最有创造性，可能不够准确

#### Max Tokens (最大令牌数)
- **范围**: 100-4000
- **推荐值**: 2000
- **说明**: 控制AI回复的最大长度
  - 值越大，回复越详细
  - 值越小，回复越简洁

### 5. 使用技巧

#### 选择合适的生成模式
- **AI简洁版**: 适合快速会议，需要简要总结
- **AI详细版**: 适合重要会议，需要完整记录
- **AI行动项提取**: 适合项目会议，关注执行事项
- **AI自定义**: 适合特殊需求，使用自定义指令

#### 自定义提示词示例
```
请按以下格式总结会议内容：
1. 会议主要议题
2. 重要决策和结论
3. 行动项和负责人
4. 下次会议安排
```

#### 优化AI效果的建议
1. **清晰的语音转录**: 确保录音环境安静
2. **详细的手动笔记**: 补充关键信息和背景
3. **准确的会议信息**: 填写完整的标题和参会人员
4. **合适的模式选择**: 根据会议类型选择生成模式

### 6. 常见问题解决

#### Q: API连接失败怎么办？
A: 
1. 检查API Key是否正确
2. 确认网络连接正常
3. 验证API服务商账户余额
4. 尝试更换API Base URL

#### Q: AI生成内容不理想？
A:
1. 调整Temperature参数
2. 增加Max Tokens限制
3. 使用自定义提示词
4. 提供更详细的输入内容

#### Q: 如何保护API Key安全？
A:
1. 不要分享您的API Key
2. 定期更换API Key
3. 监控API使用量
4. 使用应用的本地存储功能

### 7. 成本控制建议

#### DeepSeek API
- 成本极低，适合大量使用
- 建议设置使用限额

#### 豆包API
- 按调用次数计费
- 建议监控使用频率

#### OpenAI API
- 按Token计费，成本较高
- 建议控制Max Tokens参数
- 优先使用gpt-3.5-turbo模型

### 8. 技术支持

如遇到配置问题，请检查：
1. 浏览器控制台错误信息
2. API服务商的状态页面
3. 网络连接和防火墙设置
4. API Key的有效期和权限

---

**祝您使用愉快！** 🎉

通过AI增强的Jason's Meeting Note，让您的会议记录更加智能和高效！
