<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jason's Meeting Note - 智能会议记录</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <header class="header">
            <h1><i class="fas fa-microphone"></i> Jason's Meeting Note</h1>
            <p class="subtitle">智能会议记录与纪要生成工具</p>
        </header>

        <!-- 会议信息设置 -->
        <section class="meeting-info">
            <div class="info-row">
                <input type="text" id="meetingTitle" placeholder="会议主题" class="input-field">
                <input type="datetime-local" id="meetingTime" class="input-field">
                <input type="text" id="participants" placeholder="参会人员（用逗号分隔）" class="input-field">
            </div>
        </section>

        <!-- 主要功能区域 -->
        <main class="main-content">
            <!-- 语音识别控制区 -->
            <section class="voice-section">
                <div class="voice-controls">
                    <button id="startRecording" class="btn btn-primary">
                        <i class="fas fa-microphone"></i> 开始录音
                    </button>
                    <button id="stopRecording" class="btn btn-secondary" disabled>
                        <i class="fas fa-stop"></i> 停止录音
                    </button>
                    <button id="pauseRecording" class="btn btn-warning" disabled>
                        <i class="fas fa-pause"></i> 暂停
                    </button>
                    <span id="recordingStatus" class="status-indicator">准备就绪</span>
                </div>
                
                <!-- 语音转录显示区 -->
                <div class="transcription-area">
                    <h3><i class="fas fa-volume-up"></i> 语音转录内容</h3>
                    <div id="transcriptionText" class="text-display">
                        <p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>
                    </div>
                    <div class="transcription-controls">
                        <button id="clearTranscription" class="btn btn-outline">
                            <i class="fas fa-trash"></i> 清空转录
                        </button>
                    </div>
                </div>
            </section>

            <!-- 手动输入区 -->
            <section class="manual-input-section">
                <h3><i class="fas fa-edit"></i> 个人笔记与关键点</h3>
                <textarea id="manualNotes" placeholder="在此输入您的个人想法、关键词、重要观点等...&#10;&#10;提示：&#10;• 记录重要决策和行动项&#10;• 标注关键人员和时间点&#10;• 补充语音识别可能遗漏的信息" class="manual-textarea"></textarea>
                <div class="input-controls">
                    <button id="clearManualNotes" class="btn btn-outline">
                        <i class="fas fa-eraser"></i> 清空笔记
                    </button>
                    <button id="addTimestamp" class="btn btn-outline">
                        <i class="fas fa-clock"></i> 插入时间戳
                    </button>
                </div>
            </section>
        </main>

        <!-- 会议纪要生成区 -->
        <section class="summary-section">
            <div class="summary-header">
                <h3><i class="fas fa-file-alt"></i> 智能会议纪要</h3>
                <button id="generateSummary" class="btn btn-success">
                    <i class="fas fa-magic"></i> 生成纪要
                </button>
            </div>
            <div id="meetingSummary" class="summary-display">
                <p class="placeholder-text">点击"生成纪要"按钮，系统将自动整合语音转录和手动笔记，生成结构化的会议纪要...</p>
            </div>
        </section>

        <!-- 操作按钮区 -->
        <section class="action-buttons">
            <button id="saveMeeting" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存会议记录
            </button>
            <button id="exportMeeting" class="btn btn-info">
                <i class="fas fa-download"></i> 导出为文档
            </button>
            <button id="newMeeting" class="btn btn-warning">
                <i class="fas fa-plus"></i> 新建会议
            </button>
            <button id="loadMeeting" class="btn btn-secondary">
                <i class="fas fa-folder-open"></i> 加载历史记录
            </button>
        </section>

        <!-- 历史记录区 -->
        <section class="history-section" style="display: none;">
            <h3><i class="fas fa-history"></i> 历史会议记录</h3>
            <div id="historyList" class="history-list">
                <!-- 历史记录将在这里动态加载 -->
            </div>
        </section>
    </div>

    <!-- 状态提示模态框 -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalMessage"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
