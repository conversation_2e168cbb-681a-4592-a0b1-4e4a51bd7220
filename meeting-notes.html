<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jason's Meeting Note - 智能会议记录</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <header class="header">
            <h1><i class="fas fa-microphone"></i> Jason's Meeting Note</h1>
            <p class="subtitle">智能会议记录与纪要生成工具</p>
        </header>

        <!-- 会议信息设置 -->
        <section class="meeting-info">
            <div class="info-row">
                <input type="text" id="meetingTitle" placeholder="会议主题" class="input-field">
                <input type="datetime-local" id="meetingTime" class="input-field">
                <input type="text" id="participants" placeholder="参会人员（用逗号分隔）" class="input-field">
            </div>
        </section>

        <!-- AI设置区域 -->
        <section class="ai-settings-section">
            <div class="ai-settings-header">
                <h3><i class="fas fa-robot"></i> AI智能助手设置</h3>
                <button id="toggleAiSettings" class="btn btn-outline">
                    <i class="fas fa-cog"></i> 配置AI
                </button>
            </div>

            <div id="aiSettingsPanel" class="ai-settings-panel" style="display: none;">
                <!-- API选择 -->
                <div class="ai-config-row">
                    <label for="apiProvider" class="config-label">选择AI服务商：</label>
                    <select id="apiProvider" class="config-select">
                        <option value="">请选择AI服务商</option>
                        <option value="deepseek">DeepSeek API</option>
                        <option value="minimax">MiniMax API</option>
                        <option value="doubao">豆包(字节跳动) API</option>
                        <option value="gemini">Google Gemini API</option>
                        <option value="openai">OpenAI GPT API</option>
                        <option value="custom">自定义API</option>
                    </select>
                </div>

                <!-- API配置区域 -->
                <div id="apiConfigArea" class="api-config-area" style="display: none;">
                    <!-- API Key -->
                    <div class="ai-config-row">
                        <label for="apiKey" class="config-label">API Key：</label>
                        <div class="password-input-group">
                            <input type="password" id="apiKey" placeholder="请输入您的API Key" class="config-input">
                            <button type="button" id="toggleApiKey" class="btn btn-outline btn-sm">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- API Base URL -->
                    <div class="ai-config-row">
                        <label for="apiBaseUrl" class="config-label">API Base URL：</label>
                        <input type="url" id="apiBaseUrl" placeholder="API基础URL" class="config-input">
                    </div>

                    <!-- 模型选择 -->
                    <div class="ai-config-row">
                        <label for="modelName" class="config-label">模型名称：</label>
                        <select id="modelName" class="config-select">
                            <option value="">请先选择API服务商</option>
                        </select>
                    </div>

                    <!-- 高级参数 -->
                    <div class="ai-config-advanced">
                        <button id="toggleAdvanced" class="btn btn-outline btn-sm">
                            <i class="fas fa-sliders-h"></i> 高级参数
                        </button>
                        <div id="advancedParams" class="advanced-params" style="display: none;">
                            <div class="param-row">
                                <label for="temperature">Temperature (0-2):</label>
                                <input type="range" id="temperature" min="0" max="2" step="0.1" value="0.7" class="param-slider">
                                <span id="temperatureValue">0.7</span>
                            </div>
                            <div class="param-row">
                                <label for="maxTokens">Max Tokens:</label>
                                <input type="number" id="maxTokens" min="100" max="4000" value="2000" class="param-input">
                            </div>
                        </div>
                    </div>

                    <!-- 连接测试 -->
                    <div class="ai-config-row">
                        <button id="testConnection" class="btn btn-info">
                            <i class="fas fa-plug"></i> 测试连接
                        </button>
                        <div id="connectionStatus" class="connection-status">
                            <span class="status-dot status-unknown"></span>
                            <span class="status-text">未测试</span>
                        </div>
                    </div>

                    <!-- 保存配置 -->
                    <div class="ai-config-row">
                        <button id="saveAiConfig" class="btn btn-success">
                            <i class="fas fa-save"></i> 保存配置
                        </button>
                        <button id="clearAiConfig" class="btn btn-warning">
                            <i class="fas fa-trash"></i> 清空配置
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 主要功能区域 -->
        <main class="main-content">
            <!-- 语音识别控制区 -->
            <section class="voice-section">
                <div class="voice-controls">
                    <button id="startRecording" class="btn btn-primary">
                        <i class="fas fa-microphone"></i> 开始录音
                    </button>
                    <button id="stopRecording" class="btn btn-secondary" disabled>
                        <i class="fas fa-stop"></i> 停止录音
                    </button>
                    <button id="pauseRecording" class="btn btn-warning" disabled>
                        <i class="fas fa-pause"></i> 暂停
                    </button>
                    <span id="recordingStatus" class="status-indicator">准备就绪</span>
                </div>
                
                <!-- 语音转录显示区 -->
                <div class="transcription-area">
                    <h3><i class="fas fa-volume-up"></i> 语音转录内容</h3>
                    <div id="transcriptionText" class="text-display">
                        <p class="placeholder-text">点击"开始录音"按钮开始语音识别...</p>
                    </div>
                    <div class="transcription-controls">
                        <button id="clearTranscription" class="btn btn-outline">
                            <i class="fas fa-trash"></i> 清空转录
                        </button>
                    </div>
                </div>
            </section>

            <!-- 手动输入区 -->
            <section class="manual-input-section">
                <h3><i class="fas fa-edit"></i> 个人笔记与关键点</h3>
                <textarea id="manualNotes" placeholder="在此输入您的个人想法、关键词、重要观点等...&#10;&#10;提示：&#10;• 记录重要决策和行动项&#10;• 标注关键人员和时间点&#10;• 补充语音识别可能遗漏的信息" class="manual-textarea"></textarea>
                <div class="input-controls">
                    <button id="clearManualNotes" class="btn btn-outline">
                        <i class="fas fa-eraser"></i> 清空笔记
                    </button>
                    <button id="addTimestamp" class="btn btn-outline">
                        <i class="fas fa-clock"></i> 插入时间戳
                    </button>
                </div>
            </section>
        </main>

        <!-- 会议纪要生成区 -->
        <section class="summary-section">
            <div class="summary-header">
                <h3><i class="fas fa-file-alt"></i> 智能会议纪要</h3>
                <div class="summary-controls">
                    <select id="summaryMode" class="summary-mode-select">
                        <option value="basic">基础整合</option>
                        <option value="ai-concise">AI简洁版</option>
                        <option value="ai-detailed">AI详细版</option>
                        <option value="ai-action">AI行动项提取</option>
                        <option value="ai-custom">AI自定义</option>
                    </select>
                    <button id="generateSummary" class="btn btn-success">
                        <i class="fas fa-magic"></i> 生成纪要
                    </button>
                </div>
            </div>

            <!-- 纠错工具栏 -->
            <div class="correction-toolbar" style="display: none;">
                <div class="toolbar-left">
                    <button id="toggleEditMode" class="btn btn-outline">
                        <i class="fas fa-edit"></i> 纠错模式
                    </button>
                    <button id="applyCorrections" class="btn btn-info" style="display: none;">
                        <i class="fas fa-check"></i> 应用纠错
                    </button>
                    <button id="cancelEdit" class="btn btn-outline" style="display: none;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
                <div class="toolbar-right">
                    <button id="showDictionary" class="btn btn-outline">
                        <i class="fas fa-book"></i> 词典管理
                    </button>
                    <span id="correctionStats" class="correction-stats">
                        已学习 <span id="learnedCount">0</span> 个词汇
                    </span>
                </div>
            </div>

            <!-- AI自定义提示词区域 -->
            <div id="customPromptArea" class="custom-prompt-area" style="display: none;">
                <label for="customPrompt" class="prompt-label">自定义AI提示词：</label>
                <textarea id="customPrompt" placeholder="请输入您希望AI如何处理会议内容的指令...&#10;&#10;例如：&#10;• 请总结会议的主要决策和行动项&#10;• 提取关键讨论点和结论&#10;• 按照议题分类整理内容" class="custom-prompt-textarea"></textarea>
            </div>

            <!-- 生成进度指示器 -->
            <div id="generationProgress" class="generation-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <p class="progress-text">AI正在分析会议内容，请稍候...</p>
            </div>

            <div id="meetingSummary" class="summary-display">
                <p class="placeholder-text">选择生成模式并点击"生成纪要"按钮，系统将自动整合语音转录和手动笔记，生成结构化的会议纪要...</p>
            </div>

            <!-- 纠错建议面板 -->
            <div id="correctionSuggestions" class="correction-suggestions" style="display: none;">
                <h4><i class="fas fa-lightbulb"></i> 纠错建议</h4>
                <div id="suggestionsList" class="suggestions-list">
                    <!-- 建议项将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- 词典管理模态框 -->
        <div id="dictionaryModal" class="modal">
            <div class="modal-content dictionary-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-book"></i> 纠错词典管理</h3>
                    <span class="close" id="closeDictionary">&times;</span>
                </div>

                <div class="dictionary-tabs">
                    <button class="tab-button active" data-tab="learned">已学习词汇</button>
                    <button class="tab-button" data-tab="statistics">统计信息</button>
                    <button class="tab-button" data-tab="settings">设置</button>
                </div>

                <div class="tab-content">
                    <!-- 已学习词汇标签页 -->
                    <div id="learnedTab" class="tab-panel active">
                        <div class="dictionary-controls">
                            <input type="text" id="searchDictionary" placeholder="搜索词汇..." class="search-input">
                            <button id="exportDictionary" class="btn btn-outline">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button id="importDictionary" class="btn btn-outline">
                                <i class="fas fa-upload"></i> 导入
                            </button>
                            <button id="clearDictionary" class="btn btn-warning">
                                <i class="fas fa-trash"></i> 清空
                            </button>
                        </div>
                        <div id="dictionaryList" class="dictionary-list">
                            <!-- 词汇列表将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 统计信息标签页 -->
                    <div id="statisticsTab" class="tab-panel">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number" id="totalCorrections">0</div>
                                <div class="stat-label">总纠错次数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="totalWords">0</div>
                                <div class="stat-label">已学习词汇</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="lastUpdate">-</div>
                                <div class="stat-label">最后更新</div>
                            </div>
                        </div>
                    </div>

                    <!-- 设置标签页 -->
                    <div id="settingsTab" class="tab-panel">
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="enableSmartCorrection" checked>
                                启用智能纠错
                            </label>
                            <p class="setting-description">自动应用已学习的纠错规则</p>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <input type="checkbox" id="enableFuzzyMatch" checked>
                                启用模糊匹配
                            </label>
                            <p class="setting-description">匹配发音相似的词汇</p>
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                匹配阈值: <span id="thresholdValue">0.7</span>
                            </label>
                            <input type="range" id="matchThreshold" min="0.5" max="0.9" step="0.1" value="0.7" class="setting-slider">
                            <p class="setting-description">相似度匹配的最低阈值</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速纠错模态框 -->
        <div id="quickCorrectionModal" class="modal">
            <div class="modal-content quick-correction-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-edit"></i> 快速纠错</h3>
                    <span class="close" id="closeQuickCorrection">&times;</span>
                </div>
                <div class="correction-form">
                    <div class="form-group">
                        <label for="wrongWord">错误词汇：</label>
                        <input type="text" id="wrongWord" class="form-input" readonly>
                    </div>
                    <div class="form-group">
                        <label for="correctWord">正确词汇：</label>
                        <input type="text" id="correctWord" class="form-input" placeholder="请输入正确的词汇">
                    </div>
                    <div class="form-group">
                        <label for="correctionContext">上下文（可选）：</label>
                        <textarea id="correctionContext" class="form-textarea" placeholder="提供上下文有助于提高纠错准确性"></textarea>
                    </div>
                    <div class="form-actions">
                        <button id="saveCorrection" class="btn btn-success">
                            <i class="fas fa-save"></i> 保存纠错
                        </button>
                        <button id="cancelCorrection" class="btn btn-outline">
                            <i class="fas fa-times"></i> 取消
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮区 -->
        <section class="action-buttons">
            <button id="saveMeeting" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存会议记录
            </button>
            <button id="exportMeeting" class="btn btn-info">
                <i class="fas fa-download"></i> 导出为文档
            </button>
            <button id="newMeeting" class="btn btn-warning">
                <i class="fas fa-plus"></i> 新建会议
            </button>
            <button id="loadMeeting" class="btn btn-secondary">
                <i class="fas fa-folder-open"></i> 加载历史记录
            </button>
        </section>

        <!-- 历史记录区 -->
        <section class="history-section" style="display: none;">
            <h3><i class="fas fa-history"></i> 历史会议记录</h3>
            <div id="historyList" class="history-list">
                <!-- 历史记录将在这里动态加载 -->
            </div>
        </section>
    </div>

    <!-- 状态提示模态框 -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalMessage"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
