<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jason's Meeting Note - 调试测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 Jason's Meeting Note 调试测试</h1>
        
        <div class="test-section">
            <h3>1. 基础元素检查</h3>
            <button class="test-button" onclick="testBasicElements()">检查基础元素</button>
            <div id="basicElementsResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 浏览器API支持检查</h3>
            <button class="test-button" onclick="testBrowserSupport()">检查浏览器支持</button>
            <div id="browserSupportResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 麦克风权限测试</h3>
            <button class="test-button" onclick="testMicrophoneAccess()">测试麦克风权限</button>
            <div id="microphoneResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 语音识别测试</h3>
            <button class="test-button" onclick="testSpeechRecognition()">测试语音识别</button>
            <button class="test-button" onclick="stopSpeechTest()">停止测试</button>
            <div id="speechResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 控制台日志</h3>
            <button class="test-button" onclick="clearConsoleLog()">清空日志</button>
            <button class="test-button" onclick="showConsoleLog()">显示日志</button>
            <div id="consoleLog" class="test-result"></div>
        </div>
    </div>

    <script>
        let testRecognition = null;
        let consoleMessages = [];
        
        // 拦截console.log
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            consoleMessages.push({type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            consoleMessages.push({type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            consoleMessages.push({type: 'warn', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalWarn.apply(console, args);
        };
        
        function testBasicElements() {
            const result = document.getElementById('basicElementsResult');
            let output = '检查基础HTML元素:\n\n';
            
            const elements = [
                'toggleAISettings',
                'aiSettingsPanel',
                'startRecording',
                'stopRecording',
                'pauseRecording',
                'recordingStatus'
            ];
            
            let allFound = true;
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    output += `✅ ${id}: 找到\n`;
                } else {
                    output += `❌ ${id}: 未找到\n`;
                    allFound = false;
                }
            });
            
            result.textContent = output;
            result.className = `test-result ${allFound ? 'success' : 'error'}`;
        }
        
        function testBrowserSupport() {
            const result = document.getElementById('browserSupportResult');
            let output = '浏览器API支持检查:\n\n';
            
            // 检查语音识别
            const hasSpeechRecognition = ('webkitSpeechRecognition' in window) || ('SpeechRecognition' in window);
            output += `🎤 语音识别API: ${hasSpeechRecognition ? '✅ 支持' : '❌ 不支持'}\n`;
            
            // 检查媒体设备
            const hasMediaDevices = navigator.mediaDevices && navigator.mediaDevices.getUserMedia;
            output += `📱 媒体设备API: ${hasMediaDevices ? '✅ 支持' : '❌ 不支持'}\n`;
            
            // 检查屏幕共享
            const hasDisplayMedia = navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia;
            output += `🖥️ 屏幕共享API: ${hasDisplayMedia ? '✅ 支持' : '❌ 不支持'}\n`;
            
            // 浏览器信息
            output += `\n🌐 浏览器信息:\n`;
            output += `User Agent: ${navigator.userAgent}\n`;
            output += `语言: ${navigator.language}\n`;
            output += `在线状态: ${navigator.onLine ? '在线' : '离线'}\n`;
            
            result.textContent = output;
            result.className = `test-result ${hasSpeechRecognition && hasMediaDevices ? 'success' : 'error'}`;
        }
        
        async function testMicrophoneAccess() {
            const result = document.getElementById('microphoneResult');
            result.textContent = '正在测试麦克风权限...';
            result.className = 'test-result info';
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                let output = '✅ 麦克风权限获取成功!\n\n';
                output += '音频轨道信息:\n';
                
                stream.getAudioTracks().forEach((track, index) => {
                    output += `轨道 ${index + 1}:\n`;
                    output += `  标签: ${track.label}\n`;
                    output += `  类型: ${track.kind}\n`;
                    output += `  启用: ${track.enabled}\n`;
                    output += `  状态: ${track.readyState}\n\n`;
                });
                
                // 停止流
                stream.getTracks().forEach(track => track.stop());
                
                result.textContent = output;
                result.className = 'test-result success';
                
            } catch (error) {
                let output = '❌ 麦克风权限获取失败!\n\n';
                output += `错误类型: ${error.name}\n`;
                output += `错误信息: ${error.message}\n\n`;
                
                if (error.name === 'NotAllowedError') {
                    output += '解决方案: 请点击地址栏的麦克风图标，选择"允许"';
                } else if (error.name === 'NotFoundError') {
                    output += '解决方案: 请检查麦克风设备连接';
                } else if (error.name === 'NotReadableError') {
                    output += '解决方案: 请关闭其他使用麦克风的程序';
                }
                
                result.textContent = output;
                result.className = 'test-result error';
            }
        }
        
        function testSpeechRecognition() {
            const result = document.getElementById('speechResult');
            
            if (testRecognition) {
                result.textContent = '语音识别已在运行中...';
                return;
            }
            
            try {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                testRecognition = new SpeechRecognition();
                
                testRecognition.continuous = true;
                testRecognition.interimResults = true;
                testRecognition.lang = 'zh-CN';
                
                let output = '🎤 语音识别测试开始...\n\n';
                result.textContent = output;
                result.className = 'test-result info';
                
                testRecognition.onstart = () => {
                    output += '✅ 语音识别已启动\n';
                    result.textContent = output;
                };
                
                testRecognition.onresult = (event) => {
                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        const transcript = event.results[i][0].transcript;
                        const confidence = event.results[i][0].confidence;
                        const isFinal = event.results[i].isFinal;
                        
                        output += `${isFinal ? '✅' : '⏳'} "${transcript}" (置信度: ${confidence?.toFixed(2) || 'N/A'})\n`;
                        result.textContent = output;
                    }
                };
                
                testRecognition.onerror = (event) => {
                    output += `❌ 错误: ${event.error}\n`;
                    result.textContent = output;
                    result.className = 'test-result error';
                    testRecognition = null;
                };
                
                testRecognition.onend = () => {
                    output += '🔄 语音识别结束\n';
                    result.textContent = output;
                    testRecognition = null;
                };
                
                testRecognition.start();
                
            } catch (error) {
                result.textContent = `❌ 语音识别初始化失败: ${error.message}`;
                result.className = 'test-result error';
            }
        }
        
        function stopSpeechTest() {
            if (testRecognition) {
                testRecognition.stop();
                testRecognition = null;
            }
        }
        
        function clearConsoleLog() {
            consoleMessages = [];
            document.getElementById('consoleLog').textContent = '日志已清空';
        }
        
        function showConsoleLog() {
            const result = document.getElementById('consoleLog');
            
            if (consoleMessages.length === 0) {
                result.textContent = '暂无日志消息';
                result.className = 'test-result info';
                return;
            }
            
            let output = `控制台日志 (共 ${consoleMessages.length} 条):\n\n`;
            
            consoleMessages.slice(-20).forEach(msg => {
                const icon = msg.type === 'error' ? '❌' : msg.type === 'warn' ? '⚠️' : 'ℹ️';
                output += `[${msg.time}] ${icon} ${msg.message}\n`;
            });
            
            result.textContent = output;
            result.className = 'test-result info';
        }
        
        // 页面加载完成后自动运行基础检查
        window.addEventListener('load', () => {
            console.log('🚀 调试页面加载完成');
            testBasicElements();
            testBrowserSupport();
        });
    </script>
</body>
</html>
