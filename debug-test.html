<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI功能调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Jason's Meeting Note - AI功能调试测试</h1>
        
        <div class="test-section">
            <h3>1. DOM元素检查</h3>
            <button class="test-button" onclick="checkDOMElements()">检查关键DOM元素</button>
            <div id="domResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 事件监听器测试</h3>
            <button class="test-button" onclick="testEventListeners()">测试事件绑定</button>
            <div id="eventResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. AI配置面板切换测试</h3>
            <button class="test-button" onclick="testTogglePanel()">测试面板切换</button>
            <div id="toggleResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. JavaScript错误检查</h3>
            <button class="test-button" onclick="checkJSErrors()">检查控制台错误</button>
            <div id="errorResult" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 实际应用测试</h3>
            <p>在新标签页中打开主应用进行测试：</p>
            <button class="test-button" onclick="openMainApp()">打开主应用</button>
        </div>
    </div>

    <script>
        let jsErrors = [];
        
        // 捕获JavaScript错误
        window.addEventListener('error', function(e) {
            jsErrors.push({
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                error: e.error
            });
        });
        
        function checkDOMElements() {
            const result = document.getElementById('domResult');
            const elements = [
                'toggleAiSettings',
                'aiSettingsPanel',
                'apiProvider',
                'apiKey',
                'toggleApiKey',
                'apiBaseUrl',
                'modelName',
                'temperature',
                'maxTokens',
                'testConnection',
                'saveAiConfig',
                'clearAiConfig'
            ];
            
            let output = '检查结果:\n';
            let allFound = true;
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    output += `✅ ${id}: 找到\n`;
                } else {
                    output += `❌ ${id}: 未找到\n`;
                    allFound = false;
                }
            });
            
            result.textContent = output;
            result.className = `test-result ${allFound ? 'success' : 'error'}`;
        }
        
        function testEventListeners() {
            const result = document.getElementById('eventResult');
            let output = '事件监听器测试:\n';
            
            try {
                // 模拟创建主应用实例
                if (typeof MeetingNoteApp !== 'undefined') {
                    output += '✅ MeetingNoteApp类已定义\n';
                    
                    // 检查是否有全局实例
                    if (window.meetingApp) {
                        output += '✅ 全局meetingApp实例存在\n';
                        
                        // 检查关键方法
                        const methods = ['toggleAISettings', 'onApiProviderChange', 'testAPIConnection'];
                        methods.forEach(method => {
                            if (typeof window.meetingApp[method] === 'function') {
                                output += `✅ ${method}方法存在\n`;
                            } else {
                                output += `❌ ${method}方法不存在\n`;
                            }
                        });
                    } else {
                        output += '❌ 全局meetingApp实例不存在\n';
                    }
                } else {
                    output += '❌ MeetingNoteApp类未定义\n';
                }
                
                result.className = 'test-result info';
            } catch (error) {
                output += `❌ 错误: ${error.message}\n`;
                result.className = 'test-result error';
            }
            
            result.textContent = output;
        }
        
        function testTogglePanel() {
            const result = document.getElementById('toggleResult');
            let output = '面板切换测试:\n';
            
            try {
                const panel = document.getElementById('aiSettingsPanel');
                const button = document.getElementById('toggleAiSettings');
                
                if (panel && button) {
                    output += '✅ 面板和按钮元素存在\n';
                    
                    // 记录初始状态
                    const initialDisplay = panel.style.display;
                    output += `初始显示状态: ${initialDisplay || 'default'}\n`;
                    
                    // 模拟点击
                    if (window.meetingApp && typeof window.meetingApp.toggleAISettings === 'function') {
                        window.meetingApp.toggleAISettings();
                        const newDisplay = panel.style.display;
                        output += `点击后显示状态: ${newDisplay}\n`;
                        
                        if (newDisplay !== initialDisplay) {
                            output += '✅ 面板状态已改变\n';
                        } else {
                            output += '❌ 面板状态未改变\n';
                        }
                    } else {
                        output += '❌ toggleAISettings方法不可用\n';
                    }
                } else {
                    output += '❌ 面板或按钮元素不存在\n';
                }
                
                result.className = 'test-result info';
            } catch (error) {
                output += `❌ 错误: ${error.message}\n`;
                result.className = 'test-result error';
            }
            
            result.textContent = output;
        }
        
        function checkJSErrors() {
            const result = document.getElementById('errorResult');
            let output = 'JavaScript错误检查:\n';
            
            if (jsErrors.length === 0) {
                output += '✅ 没有检测到JavaScript错误\n';
                result.className = 'test-result success';
            } else {
                output += `❌ 检测到 ${jsErrors.length} 个错误:\n\n`;
                jsErrors.forEach((error, index) => {
                    output += `错误 ${index + 1}:\n`;
                    output += `消息: ${error.message}\n`;
                    output += `文件: ${error.filename}\n`;
                    output += `行号: ${error.lineno}:${error.colno}\n\n`;
                });
                result.className = 'test-result error';
            }
            
            result.textContent = output;
        }
        
        function openMainApp() {
            window.open('meeting-notes.html', '_blank');
        }
        
        // 页面加载完成后自动运行基础检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkDOMElements();
                checkJSErrors();
            }, 1000);
        });
    </script>
    
    <!-- 加载主应用的脚本进行测试 -->
    <script src="script.js"></script>
</body>
</html>
