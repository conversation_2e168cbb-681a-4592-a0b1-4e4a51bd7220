<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作周报AI编辑器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        accent: '#6366F1',
                        dark: '#1E293B',
                        light: '#F8FAFC'
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .transition-height {
                transition: max-height 0.5s ease-in-out;
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
        }
    </style>
</head>
<body class="font-inter bg-gray-50 text-gray-800 min-h-screen flex flex-col">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50 transition-all duration-300" id="header">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <i class="fa-solid fa-file-text text-primary text-2xl"></i>
                <h1 class="text-xl md:text-2xl font-bold text-dark">工作周报AI<span class="text-primary">编辑器</span></h1>
            </div>
            <div class="flex items-center space-x-4">
                <button id="apiKeyBtn" class="flex items-center px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                    <i class="fa-solid fa-key mr-1.5"></i> API设置
                </button>
                <button id="saveBtn" class="hidden md:flex items-center px-3 py-1.5 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors">
                    <i class="fa-solid fa-save mr-1.5"></i> 保存
                </button>
                <button id="exportBtn" class="hidden md:flex items-center px-3 py-1.5 bg-secondary text-white rounded-md hover:bg-secondary/90 transition-colors shadow-sm">
                    <i class="fa-solid fa-download mr-1.5"></i> 导出
                </button>
                <button id="mobileMenuBtn" class="md:hidden text-gray-600 hover:text-primary">
                    <i class="fa-solid fa-bars text-xl"></i>
                </button>
            </div>
        </div>
        <!-- 移动端菜单 -->
        <div id="mobileMenu" class="hidden md:hidden bg-white border-t border-gray-100 shadow-inner">
            <div class="container mx-auto px-4 py-2 flex flex-col space-y-2">
                <button id="mobileApiKeyBtn" class="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                    <i class="fa-solid fa-key mr-2"></i> API设置
                </button>
                <button class="flex items-center px-3 py-2 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors">
                    <i class="fa-solid fa-save mr-2"></i> 保存
                </button>
                <button class="flex items-center px-3 py-2 bg-secondary text-white rounded-md hover:bg-secondary/90 transition-colors shadow-sm">
                    <i class="fa-solid fa-download mr-2"></i> 导出
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容区 -->
    <main class="flex-grow container mx-auto px-4 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧：输入区 -->
            <div class="lg:col-span-1 space-y-6">
                <!-- 多客户项目工作记录导入 -->
                <div class="bg-white rounded-xl shadow-sm p-5 border border-gray-100 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-semibold flex items-center">
                            <i class="fa-solid fa-upload text-primary mr-2"></i> 多客户项目工作记录
                        </h2>
                        <div class="flex items-center space-x-2">
                            <button
                                id="addProjectBtn"
                                class="px-3 py-1.5 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors text-sm"
                            >
                                <i class="fa-solid fa-plus mr-1"></i> 添加项目
                            </button>
                            <button
                                id="batchGenerateBtn"
                                class="px-3 py-1.5 bg-secondary text-white rounded-md hover:bg-secondary/90 transition-colors text-sm font-medium"
                            >
                                <i class="fa-solid fa-magic mr-1"></i> 批量生成
                            </button>
                        </div>
                    </div>

                    <div id="projectInputs" class="space-y-4">
                        <!-- 默认项目输入框 -->
                        <div class="project-input-group border border-gray-200 rounded-lg p-4" data-project-id="1">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-medium text-gray-700 flex items-center">
                                    <i class="fa-solid fa-building mr-2 text-blue-500"></i>
                                    <input type="text" class="project-name-input bg-transparent border-none outline-none font-medium text-gray-700 w-32" placeholder="项目名称" value="项目一">
                                </h3>
                                <div class="flex items-center space-x-2">
                                    <button class="generate-single-btn px-2 py-1 bg-primary/10 text-primary rounded text-xs hover:bg-primary/20 transition-colors">
                                        <i class="fa-solid fa-play mr-1"></i> 单独生成
                                    </button>
                                    <button class="remove-project-btn px-2 py-1 text-red-500 hover:bg-red-50 rounded text-xs" style="display: none;">
                                        <i class="fa-solid fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <textarea
                                class="work-record-input w-full h-24 p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all placeholder-gray-400"
                                placeholder="请输入该客户项目的工作记录...&#10;例如：业务背景、竞品情况、进展现状等"
                            ></textarea>
                        </div>
                    </div>

                    <div class="flex flex-col gap-2 mt-4 pt-4 border-t border-gray-100">
                        <button id="analyzeBtn" class="w-full px-4 py-2.5 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors shadow-sm flex items-center justify-center">
                            <i class="fa-solid fa-magic mr-2"></i> 智能分析（单项目模式）
                        </button>
                        <button id="clearAllBtn" class="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center">
                            <i class="fa-solid fa-trash mr-2"></i> 清空所有项目
                        </button>
                    </div>
                </div>

                <!-- API设置 -->
                <div class="bg-white rounded-xl shadow-sm p-5 border border-gray-100 hover:shadow-md transition-shadow">
                    <h2 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fa-solid fa-key text-primary mr-2"></i> API设置
                    </h2>
                    <div class="space-y-4">
                        <!-- AI模型选择 -->
                        <div>
                            <label for="aiProviderSelect" class="block text-sm font-medium text-gray-700 mb-2">选择AI模型</label>
                            <select id="aiProviderSelect" class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all">
                                <option value="deepseek">DeepSeek</option>
                                <option value="openai">OpenAI GPT</option>
                                <option value="anthropic">Anthropic Claude</option>
                                <option value="gemini">Google Gemini</option>
                            </select>
                        </div>

                        <!-- API Key输入 -->
                        <div class="relative">
                            <label for="apiKeyInput" class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                            <input type="password" id="apiKeyInput" placeholder="输入你的API Key"
                                class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all">
                            <button id="toggleApiKeyBtn" class="absolute right-3 top-9 transform text-gray-400 hover:text-gray-600">
                                <i class="fa-solid fa-eye-slash"></i>
                            </button>
                        </div>

                        <!-- 格式提示 -->
                        <div id="apiKeyFormatHint" class="text-xs text-gray-500 bg-blue-50 p-2 rounded border-l-2 border-blue-200">
                            <i class="fa-solid fa-info-circle mr-1 text-blue-500"></i>
                            <span>DeepSeek API Key格式: sk-xxxxxxxx</span>
                        </div>

                        <!-- 示例 -->
                        <div id="apiKeyExample" class="text-xs text-gray-400 bg-gray-50 p-2 rounded">
                            <i class="fa-solid fa-lightbulb mr-1"></i>
                            <span>示例: sk-1234567890abcdef1234567890abcdef</span>
                        </div>

                        <!-- 获取API Key链接 -->
                        <div>
                            <a id="getApiKeyLink" href="https://platform.deepseek.com/" target="_blank" class="text-xs text-primary hover:text-primary/80 transition-colors">
                                <i class="fa-solid fa-external-link mr-1"></i> 如何获取DeepSeek API Key?
                            </a>
                        </div>

                        <!-- 错误提示 -->
                        <div id="apiKeyError" class="text-red-500 text-sm hidden">
                            <i class="fa-solid fa-exclamation-circle mr-1"></i>
                            <span>API Key格式不正确</span>
                        </div>

                        <!-- 成功提示 -->
                        <div id="apiKeySuccess" class="text-green-500 text-sm hidden">
                            <i class="fa-solid fa-check-circle mr-1"></i>
                            <span>API连接成功</span>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-2">
                                <button id="saveApiKeyBtn" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors shadow-sm flex items-center">
                                    <i class="fa-solid fa-save mr-2"></i> 保存设置
                                </button>
                                <button id="testApiKeyBtn" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors shadow-sm flex items-center">
                                    <i class="fa-solid fa-vial mr-2"></i> 测试连接
                                </button>
                            </div>
                            <div id="apiStatus" class="text-sm hidden">
                                <i class="fa-solid fa-circle text-green-500 mr-1"></i>
                                <span>已连接</span>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- 提示区 -->
                <div class="bg-primary/5 rounded-xl p-4 border border-primary/20">
                    <h3 class="font-medium text-primary flex items-center mb-2">
                        <i class="fa-solid fa-lightbulb mr-2"></i> 提示
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-1.5">
                        <li class="flex items-start">
                            <i class="fa-solid fa-check-circle text-secondary mt-0.5 mr-2"></i>
                            <span>输入可以是工作日志、聊天记录、邮件摘要等零散内容</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fa-solid fa-check-circle text-secondary mt-0.5 mr-2"></i>
                            <span>系统会自动整理成结构化的周报格式</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fa-solid fa-check-circle text-secondary mt-0.5 mr-2"></i>
                            <span>系统会根据内容添加适当的分类标题</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 右侧：输出区 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 处理状态 -->
                <div id="processingStatus" class="bg-white rounded-xl shadow-sm p-5 border border-gray-100 hidden">
                    <div class="flex items-center">
                        <div id="statusSpinner" class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary mr-4"></div>
                        <div>
                            <h3 class="font-medium text-gray-800">正在处理...</h3>
                            <p class="text-sm text-gray-500" id="statusMessage">AI正在分析你的工作记录，请稍候...</p>
                        </div>
                    </div>
                </div>

                <!-- 已完成的周报 -->
                <div id="reportContainer" class="space-y-6 hidden">
                    <!-- 合并的工作周报内容 -->
                    <div class="bg-white rounded-xl shadow-sm border-2 border-primary/20 hover:border-primary/40 transition-all duration-300">
                        <div class="bg-gradient-to-r from-primary/5 to-primary/10 px-5 py-3 rounded-t-xl border-b border-primary/20">
                            <h2 class="text-lg font-semibold flex items-center text-primary">
                                <i class="fa-solid fa-file-text mr-2"></i> 工作周报
                            </h2>
                        </div>
                        <div class="p-5">
                            <div id="reportContent"
                                 class="prose max-w-none space-y-6 min-h-[200px] p-6 bg-gray-50/50 rounded-lg border border-gray-200 focus-within:bg-white focus-within:border-primary/50 transition-all duration-200 cursor-text select-text"
                                 tabindex="0"
                                 role="textbox"
                                 aria-label="工作周报内容">
                                <!-- 工作周报内容将动态添加 -->
                                <div id="workSummary" class="space-y-4">
                                    <!-- 工作总结内容 -->
                                </div>
                                <div id="nextSteps" class="space-y-4">
                                    <!-- 下一步计划内容 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 编辑操作区 -->
                    <div class="flex flex-wrap gap-3 justify-between items-center bg-white rounded-xl shadow-sm p-4 border border-gray-100">
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fa-solid fa-clock-o mr-1.5"></i>
                            <span id="reportDate">2025年5月22日</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <button id="editSummaryBtn" class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center">
                                <i class="fa-solid fa-pencil mr-1.5"></i> 编辑总结
                            </button>
                            <button id="editStepsBtn" class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center">
                                <i class="fa-solid fa-pencil mr-1.5"></i> 编辑计划
                            </button>
                            <button id="regenerateBtn" class="px-3 py-1.5 bg-accent text-white rounded-md hover:bg-accent/90 transition-colors flex items-center">
                                <i class="fa-solid fa-refresh mr-1.5"></i> 重新生成
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <div class="flex items-center space-x-2">
                        <i class="fa-solid fa-file-text text-primary text-xl"></i>
                        <span class="font-semibold text-lg">工作周报AI编辑器</span>
                    </div>
                    <p class="text-gray-400 text-sm mt-1">智能整理你的工作记录，生成专业周报</p>
                </div>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fa-solid fa-question-circle"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fa-solid fa-cog"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fa-solid fa-user-circle"></i>
                    </a>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-5 pt-5 text-center text-gray-400 text-sm">
                &copy; 2025 工作周报AI编辑器 | 保护你的隐私，所有内容仅本地处理
            </div>
        </div>
    </footer>

    <!-- 编辑模态框 -->
    <div id="editModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col">
            <div class="p-5 border-b border-gray-100 flex justify-between items-center">
                <h3 class="font-semibold text-lg" id="modalTitle">编辑内容</h3>
                <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600">
                    <i class="fa-solid fa-times"></i>
                </button>
            </div>
            <div class="p-5 flex-grow overflow-y-auto">
                <textarea id="editContent" class="w-full h-full p-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all resize-none"></textarea>
            </div>
            <div class="p-5 border-t border-gray-100 flex justify-end space-x-3">
                <button id="cancelEditBtn" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                    取消
                </button>
                <button id="saveEditBtn" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors shadow-sm">
                    保存修改
                </button>
            </div>
        </div>
    </div>

    <script>
        // DOM元素
        const workRecordInput = document.querySelector('.work-record-input'); // 更新为第一个项目的输入框
        const analyzeBtn = document.getElementById('analyzeBtn');
        const processingStatus = document.getElementById('processingStatus');
        const reportContainer = document.getElementById('reportContainer');
        const reportContent = document.getElementById('reportContent');
        const reportDate = document.getElementById('reportDate');
        const editReportBtn = document.getElementById('editReportBtn');
        const regenerateBtn = document.getElementById('regenerateBtn');

        // 多项目相关DOM元素
        const projectInputs = document.getElementById('projectInputs');
        const addProjectBtn = document.getElementById('addProjectBtn');
        const batchGenerateBtn = document.getElementById('batchGenerateBtn');
        const clearAllBtn = document.getElementById('clearAllBtn');
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');
        const header = document.getElementById('header');
        const saveBtn = document.getElementById('saveBtn');
        const exportBtn = document.getElementById('exportBtn');
        const editModal = document.getElementById('editModal');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const cancelEditBtn = document.getElementById('cancelEditBtn');
        const saveEditBtn = document.getElementById('saveEditBtn');
        const editContent = document.getElementById('editContent');
        const modalTitle = document.getElementById('modalTitle');
        const statusMessage = document.getElementById('statusMessage');
        const apiKeyBtn = document.getElementById('apiKeyBtn');
        const mobileApiKeyBtn = document.getElementById('mobileApiKeyBtn');
        const apiKeyInput = document.getElementById('apiKeyInput');
        const toggleApiKeyBtn = document.getElementById('toggleApiKeyBtn');
        const saveApiKeyBtn = document.getElementById('saveApiKeyBtn');
        const testApiKeyBtn = document.getElementById('testApiKeyBtn');
        const apiStatus = document.getElementById('apiStatus');
        const aiProviderSelect = document.getElementById('aiProviderSelect');
        const apiKeyFormatHint = document.getElementById('apiKeyFormatHint');
        const apiKeyExample = document.getElementById('apiKeyExample');
        const getApiKeyLink = document.getElementById('getApiKeyLink');
        const apiKeyError = document.getElementById('apiKeyError');
        const apiKeySuccess = document.getElementById('apiKeySuccess');
        const workSummary = document.getElementById('workSummary');
        const nextSteps = document.getElementById('nextSteps');
        const editSummaryBtn = document.getElementById('editSummaryBtn');
        const editStepsBtn = document.getElementById('editStepsBtn');

        // 存储当前编辑的内容类型
        let currentEditType = '';
        // API连接状态
        let isApiConnected = false;
        // 项目计数器
        let projectCounter = 1;
        // 存储多项目生成的结果
        let multiProjectResults = {};

        // 设置当前日期
        const setCurrentDate = () => {
            const now = new Date();
            const year = now.getFullYear();
            const month = now.getMonth() + 1;
            const day = now.getDate();
            reportDate.textContent = `${year}年${month}月${day}日`;
        };

        // 多项目管理功能
        const addNewProject = () => {
            projectCounter++;
            const projectHtml = `
                <div class="project-input-group border border-gray-200 rounded-lg p-4" data-project-id="${projectCounter}">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-medium text-gray-700 flex items-center">
                            <i class="fa-solid fa-building mr-2 text-blue-500"></i>
                            <input type="text" class="project-name-input bg-transparent border-none outline-none font-medium text-gray-700 w-32" placeholder="项目名称" value="项目${projectCounter}">
                        </h3>
                        <div class="flex items-center space-x-2">
                            <button class="generate-single-btn px-2 py-1 bg-primary/10 text-primary rounded text-xs hover:bg-primary/20 transition-colors">
                                <i class="fa-solid fa-play mr-1"></i> 单独生成
                            </button>
                            <button class="remove-project-btn px-2 py-1 text-red-500 hover:bg-red-50 rounded text-xs">
                                <i class="fa-solid fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <textarea
                        class="work-record-input w-full h-24 p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all placeholder-gray-400"
                        placeholder="请输入该客户项目的工作记录...&#10;例如：业务背景、竞品情况、进展现状等"
                    ></textarea>
                </div>
            `;

            projectInputs.insertAdjacentHTML('beforeend', projectHtml);
            updateRemoveButtonsVisibility();
            setupProjectEventListeners();
        };

        const removeProject = (projectElement) => {
            const projectId = projectElement.dataset.projectId;
            delete multiProjectResults[projectId];
            projectElement.remove();
            updateRemoveButtonsVisibility();
        };

        const updateRemoveButtonsVisibility = () => {
            const projectGroups = document.querySelectorAll('.project-input-group');
            projectGroups.forEach(group => {
                const removeBtn = group.querySelector('.remove-project-btn');
                if (projectGroups.length > 1) {
                    removeBtn.style.display = 'block';
                } else {
                    removeBtn.style.display = 'none';
                }
            });
        };

        const clearAllProjects = () => {
            if (confirm('确定要清空所有项目的内容吗？')) {
                document.querySelectorAll('.work-record-input').forEach(input => {
                    input.value = '';
                });
                multiProjectResults = {};
                showSuccessNotification('已清空所有项目内容');
            }
        };

        const setupProjectEventListeners = () => {
            // 为所有单独生成按钮添加事件监听
            document.querySelectorAll('.generate-single-btn').forEach(btn => {
                btn.replaceWith(btn.cloneNode(true)); // 移除旧的事件监听器
            });

            document.querySelectorAll('.generate-single-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const projectGroup = e.target.closest('.project-input-group');
                    const projectId = projectGroup.dataset.projectId;
                    const projectName = projectGroup.querySelector('.project-name-input').value;
                    const workContent = projectGroup.querySelector('.work-record-input').value;

                    if (!workContent.trim()) {
                        alert('请输入工作记录内容');
                        return;
                    }

                    generateSingleProject(projectId, projectName, workContent);
                });
            });

            // 为所有删除按钮添加事件监听
            document.querySelectorAll('.remove-project-btn').forEach(btn => {
                btn.replaceWith(btn.cloneNode(true)); // 移除旧的事件监听器
            });

            document.querySelectorAll('.remove-project-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const projectGroup = e.target.closest('.project-input-group');
                    removeProject(projectGroup);
                });
            });
        };

        // AI服务提供商配置
        const aiProviders = {
            deepseek: {
                name: 'DeepSeek',
                apiEndpoint: 'https://api.deepseek.com/v1/chat/completions',
                keyFormat: /^sk-[A-Za-z0-9]{20,}$/,
                keyFormatHint: 'DeepSeek API Key格式: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (以sk-开头，后跟字母数字)',
                keyExample: 'sk-1234567890abcdef1234567890abcdef',
                getKeyLink: 'https://platform.deepseek.com/',
                getLinkText: '如何获取DeepSeek API Key?',
                model: 'deepseek-chat',
                requestFormat: (prompt) => ({
                    model: 'deepseek-chat',
                    messages: [{ role: 'user', content: prompt }],
                    temperature: 0.7,
                    max_tokens: 1000
                })
            },
            openai: {
                name: 'OpenAI',
                apiEndpoint: 'https://api.openai.com/v1/chat/completions',
                keyFormat: /^sk-[A-Za-z0-9]{20,}$/,
                keyFormatHint: 'OpenAI API Key格式: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (以sk-开头，后跟字母数字)',
                keyExample: 'sk-1234567890abcdef1234567890abcdef1234567890abcdef',
                getKeyLink: 'https://platform.openai.com/api-keys',
                getLinkText: '如何获取OpenAI API Key?',
                model: 'gpt-3.5-turbo',
                requestFormat: (prompt) => ({
                    model: 'gpt-3.5-turbo',
                    messages: [{ role: 'user', content: prompt }],
                    temperature: 0.7,
                    max_tokens: 1000
                })
            },
            anthropic: {
                name: 'Anthropic Claude',
                apiEndpoint: 'https://api.anthropic.com/v1/messages',
                keyFormat: /^sk-ant-[A-Za-z0-9_-]{20,}$/,
                keyFormatHint: 'Anthropic API Key格式: sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (以sk-ant-开头)',
                keyExample: 'sk-ant-api03-1234567890abcdef1234567890abcdef',
                getKeyLink: 'https://console.anthropic.com/keys',
                getLinkText: '如何获取Anthropic API Key?',
                model: 'claude-3-haiku-20240307',
                requestFormat: (prompt) => ({
                    model: 'claude-3-haiku-20240307',
                    messages: [{ role: 'user', content: prompt }],
                    max_tokens: 1000
                })
            },
            gemini: {
                name: 'Google Gemini',
                apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
                keyFormat: /^[A-Za-z0-9_-]{20,}$/,
                keyFormatHint: 'Google API Key格式: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (纯字母数字字符串)',
                keyExample: 'AIzaSyDhGjCvK1234567890abcdefghijklmnop',
                getKeyLink: 'https://ai.google.dev/',
                getLinkText: '如何获取Google Gemini API Key?',
                model: 'gemini-pro',
                requestFormat: (prompt) => ({
                    contents: [{ parts: [{ text: prompt }] }],
                    generationConfig: {
                        temperature: 0.7,
                        maxOutputTokens: 1000
                    }
                })
            }
        };

        // 获取当前选择的AI服务提供商配置
        const getCurrentProvider = () => {
            const providerId = aiProviderSelect.value;
            return aiProviders[providerId];
        };

        // 显示API错误信息
        const showApiError = (message) => {
            apiKeyError.querySelector('span').textContent = message;
            apiKeyError.classList.remove('hidden');
            apiKeySuccess.classList.add('hidden');
            apiStatus.classList.add('hidden');
            isApiConnected = false;
        };

        // 显示API成功信息
        const showApiSuccess = () => {
            apiKeySuccess.classList.remove('hidden');
            apiKeyError.classList.add('hidden');
            apiStatus.classList.remove('hidden');
            isApiConnected = true;
        };

        // 显示错误通知
        const showErrorNotification = (message) => {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-0 max-w-md';
            notification.innerHTML = `
                <div class="flex items-start">
                    <i class="fa-solid fa-exclamation-triangle mr-3 mt-0.5 flex-shrink-0"></i>
                    <div class="flex-1">
                        <div class="font-semibold mb-1">操作失败</div>
                        <div class="text-sm opacity-90">${message}</div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
                        <i class="fa-solid fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(notification);

            // 自动消失
            setTimeout(() => {
                notification.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        };

        // 显示成功通知
        const showSuccessNotification = (message) => {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-0 max-w-md';
            notification.innerHTML = `
                <div class="flex items-start">
                    <i class="fa-solid fa-check-circle mr-3 mt-0.5 flex-shrink-0"></i>
                    <div class="flex-1">
                        <div class="font-semibold mb-1">操作成功</div>
                        <div class="text-sm opacity-90">${message}</div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
                        <i class="fa-solid fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(notification);

            // 自动消失
            setTimeout(() => {
                notification.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        };

        // 更新API Key格式提示和获取链接
        const updateProviderUI = () => {
            const provider = getCurrentProvider();

            // 更新格式提示
            apiKeyFormatHint.innerHTML = `<i class="fa-solid fa-info-circle mr-1 text-blue-500"></i><span>${provider.keyFormatHint}</span>`;

            // 更新示例
            apiKeyExample.innerHTML = `<i class="fa-solid fa-lightbulb mr-1"></i><span>示例: ${provider.keyExample}</span>`;

            // 更新获取链接
            getApiKeyLink.href = provider.getKeyLink;
            getApiKeyLink.innerHTML = `<i class="fa-solid fa-external-link mr-1"></i> ${provider.getLinkText}`;

            // 清除之前的错误和成功消息
            apiKeyError.classList.add('hidden');
            apiKeySuccess.classList.add('hidden');

            // 如果有保存的API Key，加载它
            const savedKey = localStorage.getItem(`${provider.name.toLowerCase()}ApiKey`);
            if (savedKey) {
                apiKeyInput.value = savedKey;
                apiStatus.classList.remove('hidden');
                isApiConnected = true;
                // 显示已保存的提示
                showApiSuccess();
            } else {
                apiKeyInput.value = '';
                apiStatus.classList.add('hidden');
                isApiConnected = false;
            }
        };

        // API Key格式验证
        const validateApiKey = (apiKey) => {
            const provider = getCurrentProvider();

            // 清理API Key - 移除前后空格和常见的无效字符
            const cleanedKey = apiKey.trim().replace(/[\r\n\t]/g, '');

            // 检查是否为空
            if (!cleanedKey) {
                return { valid: false, error: '请输入API Key' };
            }

            // 检查长度
            if (cleanedKey.length < 20) {
                return { valid: false, error: `API Key长度太短，${provider.name} API Key通常至少20个字符` };
            }

            // 检查格式
            if (!provider.keyFormat.test(cleanedKey)) {
                let specificError = '';
                switch(provider.name.toLowerCase()) {
                    case 'deepseek':
                        if (!cleanedKey.startsWith('sk-')) {
                            specificError = 'DeepSeek API Key必须以"sk-"开头';
                        } else {
                            specificError = 'DeepSeek API Key格式不正确，应为: sk-后跟字母数字字符';
                        }
                        break;
                    case 'openai':
                        if (!cleanedKey.startsWith('sk-')) {
                            specificError = 'OpenAI API Key必须以"sk-"开头';
                        } else {
                            specificError = 'OpenAI API Key格式不正确，应为: sk-后跟字母数字字符';
                        }
                        break;
                    case 'anthropic':
                        if (!cleanedKey.startsWith('sk-ant-')) {
                            specificError = 'Anthropic API Key必须以"sk-ant-"开头';
                        } else {
                            specificError = 'Anthropic API Key格式不正确，应为: sk-ant-后跟字母数字字符';
                        }
                        break;
                    case 'gemini':
                        specificError = 'Google Gemini API Key应为纯字母数字字符串（可包含下划线和连字符）';
                        break;
                    default:
                        specificError = `${provider.name} API Key格式不正确`;
                }
                return { valid: false, error: specificError };
            }

            return { valid: true, cleanedKey: cleanedKey };
        };

        // 保存API Key
        const saveApiKey = () => {
            const apiKey = apiKeyInput.value;
            const provider = getCurrentProvider();

            // 验证API Key
            const validation = validateApiKey(apiKey);

            if (!validation.valid) {
                showApiError(validation.error);
                return;
            }

            // 使用清理后的API Key
            const cleanedKey = validation.cleanedKey;

            // 更新输入框为清理后的值
            apiKeyInput.value = cleanedKey;

            // 保存到本地存储
            localStorage.setItem(`${provider.name.toLowerCase()}ApiKey`, cleanedKey);
            localStorage.setItem('selectedAiProvider', provider.name.toLowerCase());

            // 显示保存成功消息
            showApiSuccess();

            // 可选：自动测试连接
            // testApiConnection(cleanedKey);
        };

        // 测试API连接
        const testApiConnection = async (apiKey = null) => {
            const provider = getCurrentProvider();

            // 如果没有传入apiKey，则使用输入框中的值
            if (!apiKey) {
                const validation = validateApiKey(apiKeyInput.value);

                if (!validation.valid) {
                    showApiError(validation.error);
                    return;
                }

                apiKey = validation.cleanedKey;
                // 更新输入框为清理后的值
                apiKeyInput.value = apiKey;
            }

            // 显示加载状态
            const originalBtnText = testApiKeyBtn.innerHTML;
            testApiKeyBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i> 测试中...';
            testApiKeyBtn.disabled = true;

            try {
                // 构建测试请求
                let requestBody;
                let headers = {
                    'Content-Type': 'application/json'
                };

                // 根据不同提供商设置不同的请求头和请求体
                switch(provider.name.toLowerCase()) {
                    case 'anthropic':
                        headers['x-api-key'] = apiKey;
                        headers['anthropic-version'] = '2023-06-01';
                        requestBody = {
                            model: provider.model,
                            messages: [{ role: 'user', content: '你好' }],
                            max_tokens: 5
                        };
                        break;
                    case 'gemini':
                        // Gemini使用URL参数传递API Key
                        const apiUrl = `${provider.apiEndpoint}?key=${apiKey}`;
                        requestBody = {
                            contents: [{ parts: [{ text: '你好' }] }],
                            generationConfig: {
                                maxOutputTokens: 5
                            }
                        };
                        const geminiResponse = await fetch(apiUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(requestBody)
                        });

                        if (!geminiResponse.ok) {
                            const errorData = await geminiResponse.json();
                            throw new Error(errorData.error?.message || '连接失败');
                        }

                        // Gemini API连接成功
                        showApiSuccess();
                        localStorage.setItem(`${provider.name.toLowerCase()}ApiKey`, apiKey);
                        return;
                    default: // OpenAI和DeepSeek使用相似的格式
                        headers['Authorization'] = `Bearer ${apiKey}`;
                        requestBody = {
                            model: provider.model,
                            messages: [{ role: 'user', content: '你好' }],
                            max_tokens: 5
                        };
                }

                // 发送测试请求
                const response = await fetch(provider.apiEndpoint, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody)
                });

                // 检查响应状态
                if (!response.ok) {
                    const errorData = await response.json();
                    let errorMessage = '连接失败';

                    if (errorData.error && errorData.error.message) {
                        errorMessage = `错误: ${errorData.error.message}`;
                    }

                    showApiError(errorMessage);
                    return;
                }

                // API连接成功
                showApiSuccess();

                // 保存验证成功的API Key
                localStorage.setItem(`${provider.name.toLowerCase()}ApiKey`, apiKey);

            } catch (error) {
                console.error('API测试错误:', error);
                showApiError(`连接失败: ${error.message || '网络错误或API服务不可用'}`);
            } finally {
                // 恢复按钮状态
                testApiKeyBtn.innerHTML = originalBtnText;
                testApiKeyBtn.disabled = false;
            }
        };

        // 使用选定的AI服务分析文本（带重试机制）
        const analyzeWithAI = async (inputText, retryCount = 0) => {
            const provider = getCurrentProvider();
            const apiKey = localStorage.getItem(`${provider.name.toLowerCase()}ApiKey`);
            const maxRetries = 2;

            if (!apiKey) {
                alert(`请先设置${provider.name} API Key`);
                return null;
            }

            // 显示处理状态
            processingStatus.classList.remove('hidden');
            reportContainer.classList.add('hidden');

            // 初始化进度指示器
            let progressStep = 0;
            const progressSteps = [
                'AI正在分析你的工作记录...',
                '正在提取关键信息...',
                '正在生成工作总结...',
                '正在制定下一步计划...',
                '正在优化内容格式...'
            ];

            statusMessage.textContent = progressSteps[0];

            // 启动进度模拟器
            const progressInterval = setInterval(() => {
                progressStep = (progressStep + 1) % progressSteps.length;
                statusMessage.textContent = progressSteps[progressStep];
            }, 1500);

            // 构建优化的提示词（支持灵活条目数量，匹配用户写作风格）
            const prompt = `请将以下工作记录整理成专业的工作周报，要求：
1. 分为"本周工作总结"和"下一步计划"两部分，每部分3-6条（根据内容重要性灵活调整）
2. 使用"业务背景"、"竞品情况"、"进展现状"、"客户内部关系"等业务术语
3. 包含具体的人名、公司名、地点等关键信息
4. 每个条目包含背景、现状、下一步行动等完整信息
5. 使用分号结尾，保持商务报告的正式性
6. 内容具体且有实际业务价值
7. 优先保持内容完整性，避免强制压缩重要信息

工作记录：
${inputText}`;

            try {
                statusMessage.textContent = `正在连接${provider.name} API...`;

                // 构建请求头和请求体
                let requestBody;
                let headers = {
                    'Content-Type': 'application/json'
                };
                let apiUrl = provider.apiEndpoint;

                // 根据不同提供商设置不同的请求头和请求体（优化参数以提高速度）
                switch(provider.name.toLowerCase()) {
                    case 'anthropic':
                        headers['x-api-key'] = apiKey;
                        headers['anthropic-version'] = '2023-06-01';
                        requestBody = {
                            model: 'claude-3-haiku-20240307', // 使用更快的模型
                            messages: [{ role: 'user', content: prompt }],
                            max_tokens: 800, // 减少token数量以提高速度
                            temperature: 0.3 // 降低温度以提高一致性和速度
                        };
                        break;
                    case 'gemini':
                        // Gemini使用URL参数传递API Key
                        apiUrl = `${provider.apiEndpoint}?key=${apiKey}`;
                        requestBody = {
                            contents: [{ parts: [{ text: prompt }] }],
                            generationConfig: {
                                temperature: 0.3,
                                maxOutputTokens: 800,
                                topP: 0.8,
                                topK: 10
                            }
                        };
                        break;
                    default: // OpenAI和DeepSeek使用相似的格式
                        headers['Authorization'] = `Bearer ${apiKey}`;
                        requestBody = {
                            model: provider.model,
                            messages: [{ role: 'user', content: prompt }],
                            temperature: 0.3, // 降低温度以提高速度
                            max_tokens: 800, // 减少token数量
                            top_p: 0.8,
                            frequency_penalty: 0.1,
                            presence_penalty: 0.1
                        };
                }

                // 发送API请求
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    let errorMessage = '未知错误';

                    if (errorData.error && errorData.error.message) {
                        errorMessage = errorData.error.message;
                    }

                    throw new Error(`API请求失败: ${response.status} - ${errorMessage}`);
                }

                statusMessage.textContent = '正在处理AI响应...';

                const data = await response.json();
                let content;

                // 根据不同提供商解析响应
                switch(provider.name.toLowerCase()) {
                    case 'anthropic':
                        content = data.content && data.content[0] && data.content[0].text;
                        break;
                    case 'gemini':
                        content = data.candidates && data.candidates[0] &&
                                  data.candidates[0].content &&
                                  data.candidates[0].content.parts &&
                                  data.candidates[0].content.parts[0] &&
                                  data.candidates[0].content.parts[0].text;
                        break;
                    default: // OpenAI和DeepSeek
                        content = data.choices && data.choices[0] &&
                                  data.choices[0].message &&
                                  data.choices[0].message.content;
                }

                // 验证响应内容
                if (!content) {
                    throw new Error(`${provider.name} API返回的数据格式不正确`);
                }

                // 解析响应内容
                return parseContent(content);

            } catch (error) {
                console.error('API调用错误:', error);

                // 检查是否需要重试
                const shouldRetry = retryCount < maxRetries && (
                    error.message.includes('网络') ||
                    error.message.includes('timeout') ||
                    error.message.includes('500') ||
                    error.message.includes('502') ||
                    error.message.includes('503')
                );

                if (shouldRetry) {
                    statusMessage.textContent = `连接失败，正在重试 (${retryCount + 1}/${maxRetries})...`;
                    await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
                    return analyzeWithAI(inputText, retryCount + 1);
                }

                // 显示更详细的错误信息
                let errorMsg = '请检查API Key或网络连接';

                if (error.message.includes('401') || error.message.includes('认证')) {
                    errorMsg = `${provider.name} API Key无效或已过期，请更新API Key`;
                    // 清除无效的API状态
                    isApiConnected = false;
                    apiStatus.classList.add('hidden');
                } else if (error.message.includes('429') || error.message.includes('超限')) {
                    errorMsg = `${provider.name} API请求次数超限，请稍后再试`;
                } else if (error.message.includes('500')) {
                    errorMsg = `${provider.name}服务器错误，请稍后再试`;
                } else if (error.message.includes('数据格式')) {
                    errorMsg = `${provider.name}服务器返回的数据格式不正确`;
                } else if (error.message.includes('网络')) {
                    errorMsg = '网络连接失败，请检查网络设置';
                }

                // 显示用户友好的错误提示
                showErrorNotification(`AI分析失败: ${errorMsg}`);
                return null;
            } finally {
                // 清除进度指示器
                if (progressInterval) {
                    clearInterval(progressInterval);
                }
                // 隐藏处理状态
                processingStatus.classList.add('hidden');
            }
        };

        // 阿拉伯数字格式化函数（使用中文顿号）
        const getNumberWithComma = (num) => {
            return `${num}、`; // 使用阿拉伯数字 + 中文顿号
        };

        // 清理格式标记和冗余符号（增强版本 - 移除粗体标记）
        const cleanFormatMarks = (text) => {
            if (!text) return '';

            // 移除Markdown格式标记（增强版本）
            text = text.replace(/\*\*(.*?)\*\*/g, '$1'); // 移除粗体标记 **text**
            text = text.replace(/\*(.*?)\*/g, '$1'); // 移除斜体标记 *text*
            text = text.replace(/_{2,}(.*?)_{2,}/g, '$1'); // 移除下划线粗体标记 __text__
            text = text.replace(/_(.*?)_/g, '$1'); // 移除下划线斜体标记 _text_
            text = text.replace(/#{1,6}\s*/g, ''); // 移除标题标记 # ## ### 等
            text = text.replace(/`{1,3}(.*?)`{1,3}/g, '$1'); // 移除代码标记 `code` 或 ```code```
            text = text.replace(/\[(.*?)\]\(.*?\)/g, '$1'); // 移除链接标记 [text](url)
            text = text.replace(/!\[(.*?)\]\(.*?\)/g, '$1'); // 移除图片标记 ![alt](url)

            // 移除HTML标签中的粗体标记
            text = text.replace(/<\/?b>/gi, ''); // 移除 <b> 和 </b>
            text = text.replace(/<\/?strong>/gi, ''); // 移除 <strong> 和 </strong>
            text = text.replace(/<\/?em>/gi, ''); // 移除 <em> 和 </em>
            text = text.replace(/<\/?i>/gi, ''); // 移除 <i> 和 </i>

            // 移除多余的特殊符号（保留中文标点）
            text = text.replace(/[~`!@#$%^&*()_+=\[\]{}|\\";'<>?]/g, ''); // 移除特殊符号，保留冒号
            text = text.replace(/[-]{2,}/g, ''); // 移除多个连字符
            text = text.replace(/[=]{2,}/g, ''); // 移除多个等号
            text = text.replace(/[.]{3,}/g, ''); // 移除多个句号

            // 清理多余的空格和换行
            text = text.replace(/\s{2,}/g, ' '); // 多个空格替换为单个空格
            text = text.replace(/\n{2,}/g, '\n'); // 多个换行替换为单个换行

            // 保留必要的中文标点符号，清理不当使用的标点
            text = text.replace(/[,，]{2,}/g, '，'); // 多个逗号替换为单个
            text = text.replace(/[;；]{2,}/g, '；'); // 多个分号替换为单个
            text = text.replace(/[:：]{2,}/g, '：'); // 多个冒号替换为单个
            text = text.replace(/[、]{2,}/g, '、'); // 多个顿号替换为单个

            return text.trim();
        };

        // 生成下一步计划的总结段落
        const generateNextStepsSummary = (nextStepsLines) => {
            if (!nextStepsLines || nextStepsLines.length === 0) {
                return null;
            }

            // 提取关键信息
            const timeKeywords = ['周', '月', '季度', '年', '天', '小时', '分钟', '节后', '前', '后', '内', '期间'];
            const actionKeywords = ['完成', '实施', '推进', '开展', '启动', '建设', '优化', '提升', '扩大', '覆盖'];
            const targetKeywords = ['目标', '预期', '计划', '达到', '实现', '提升', '增长', '覆盖', '范围'];

            let timeInfo = [];
            let actionInfo = [];
            let targetInfo = [];

            // 分析每一行内容
            nextStepsLines.forEach(line => {
                const cleanLine = line.replace(/^\s*(?:[0-9一二三四五六七八九十]+[\.。、）\)：:]+\s*|[•\-\*]\s+)/g, '').trim();

                // 提取时间信息
                timeKeywords.forEach(keyword => {
                    if (cleanLine.includes(keyword)) {
                        const timeMatch = cleanLine.match(new RegExp(`[^，。；]*${keyword}[^，。；]*`, 'g'));
                        if (timeMatch) {
                            timeInfo.push(...timeMatch);
                        }
                    }
                });

                // 提取行动信息
                actionKeywords.forEach(keyword => {
                    if (cleanLine.includes(keyword)) {
                        const actionMatch = cleanLine.match(new RegExp(`[^，。；]*${keyword}[^，。；]*`, 'g'));
                        if (actionMatch) {
                            actionInfo.push(...actionMatch);
                        }
                    }
                });

                // 提取目标信息
                targetKeywords.forEach(keyword => {
                    if (cleanLine.includes(keyword)) {
                        const targetMatch = cleanLine.match(new RegExp(`[^，。；]*${keyword}[^，。；]*`, 'g'));
                        if (targetMatch) {
                            targetInfo.push(...targetMatch);
                        }
                    }
                });
            });

            // 去重并限制数量
            timeInfo = [...new Set(timeInfo)].slice(0, 2);
            actionInfo = [...new Set(actionInfo)].slice(0, 3);
            targetInfo = [...new Set(targetInfo)].slice(0, 2);

            // 生成总结段落
            let summary = '';

            if (actionInfo.length > 0) {
                summary += `本阶段工作将重点围绕${actionInfo.slice(0, 2).join('、')}等关键任务展开`;
            } else {
                summary += '本阶段将按计划推进各项重点工作';
            }

            if (timeInfo.length > 0) {
                const timeText = timeInfo[0].replace(/[，。；]/g, '');
                summary += `，预计在${timeText}完成主要里程碑节点`;
            }

            if (targetInfo.length > 0) {
                const targetText = targetInfo[0].replace(/[，。；]/g, '');
                summary += `，力争${targetText}`;
            } else {
                summary += '，确保各项工作目标如期达成';
            }

            summary += '。整体工作将采用分阶段推进的方式，确保质量与进度的有机统一。';

            return summary;
        };

        // 解析响应内容
        const parseContent = (content) => {
            // 分割内容为工作总结和下一步计划
            const lines = content.split('\n').filter(line => line.trim());
            let summaryLines = [];
            let nextStepsLines = [];
            let currentSection = 'summary'; // 默认开始是总结部分

            for (let line of lines) {
                // 检查是否是"下一步计划"相关的标题
                if (line.match(/^(下一步计划|工作计划|下一步推进计划|下周计划|后续计划)[:：]?/i)) {
                    currentSection = 'nextSteps';
                    continue; // 跳过标题行，避免重复显示
                }
                // 检查是否是"工作总结"相关的标题
                else if (line.match(/^(本周工作总结|工作总结|本周总结|工作内容)[:：]?/i)) {
                    currentSection = 'summary';
                    continue; // 跳过标题行，避免重复显示
                }

                // 过滤掉可能的重复区域标题
                const duplicateTitleRegex = /^(本周工作总结|下一步计划|工作总结|下一步推进计划)$/i;
                if (duplicateTitleRegex.test(line.trim())) {
                    continue; // 跳过重复的区域标题
                }

                // 根据当前部分添加内容
                if (currentSection === 'summary') {
                    summaryLines.push(line);
                } else {
                    nextStepsLines.push(line);
                }
            }

            // 处理内容，使用阿拉伯数字序号，每个条目为完整段落描述，智能调整条目数量（优化版本）
            const processContent = (lines) => {
                if (!lines || lines.length === 0) return '';

                const processedLines = [];
                let itemCounter = 0;
                // 移除固定限制，根据内容量智能调整，允许5-6条
                const MIN_ITEMS = 3; // 最少条目数
                const MAX_ITEMS = 6; // 最大条目数量限制
                const PREFERRED_ITEMS = 4; // 首选条目数

                // 预编译正则表达式以提高性能
                const cleanRegex = /^\s*(?:[0-9一二三四五六七八九十]+[\.。、）\)：:]+\s*|[•\-\*]\s+|[（\(][0-9一二三四五六七八九十]+[）\)]\s*)/g;
                const categoryTitles = ["客户业务场景", "客户关注点", "客户痛点", "竞品情况", "技术方案", "项目进展", "工作内容", "完成情况", "对接团队情况", "现状分析", "友商对接情况", "POC预期推进计划"];
                const titleRegex = new RegExp(`^(${categoryTitles.join('|')})[:：]?`, 'i');

                // 智能分组和合并：将相关内容合并为完整段落
                const consolidatedItems = [];
                let currentItem = { content: '', relatedLines: [] };

                for (let line of lines) {
                    // 清理行内容，移除各种序号和标记（使用预编译的正则表达式）
                    line = line.replace(cleanRegex, '').trim();

                    // 检查是否是分类标题（使用预编译的正则表达式）
                    const titleMatch = line.match(titleRegex);

                    if (titleMatch) {
                        // 如果之前有内容，先保存
                        if (currentItem.content || currentItem.relatedLines.length > 0) {
                            consolidatedItems.push(currentItem);
                        }

                        // 开始新的条目，将分类标题作为内容的开头
                        currentItem = {
                            content: titleMatch[1],
                            relatedLines: [],
                            priority: 1 // 分类标题优先级高
                        };

                        // 移除标题部分，保留后续内容
                        line = line.replace(titleRegex, '').trim();
                        if (line) {
                            currentItem.relatedLines.push(line);
                        }
                    } else if (line) {
                        // 检查是否是新的主要内容点（通常较长且包含关键信息）
                        if (line.length > 20 && (line.includes('，') || line.includes('；') || line.includes('、'))) {
                            // 如果之前有内容，先保存
                            if (currentItem.content || currentItem.relatedLines.length > 0) {
                                consolidatedItems.push(currentItem);
                            }

                            // 开始新的条目
                            currentItem = {
                                content: line,
                                relatedLines: [],
                                priority: 2 // 主要内容优先级中等
                            };
                        } else {
                            // 作为相关信息添加到当前条目
                            currentItem.relatedLines.push(line);
                        }
                    }
                }

                // 添加最后一个条目
                if (currentItem.content || currentItem.relatedLines.length > 0) {
                    consolidatedItems.push(currentItem);
                }

                // 智能条目数量调整：根据内容重要性和完整性决定最终条目数
                let finalItems = [...consolidatedItems];

                // 计算每个条目的重要性得分
                finalItems.forEach(item => {
                    let score = item.priority || 3;

                    // 根据内容长度调整得分
                    const contentLength = (item.content || '').length + (item.relatedLines || []).join('').length;
                    if (contentLength > 50) score -= 0.5; // 内容丰富的条目优先保留
                    if (contentLength > 100) score -= 0.5;

                    // 根据关键词调整得分
                    const fullText = (item.content || '') + (item.relatedLines || []).join('');
                    const keyWords = ['客户', '项目', '合作', '签约', '推进', '完成', '目标', '计划'];
                    const keyWordCount = keyWords.filter(word => fullText.includes(word)).length;
                    score -= keyWordCount * 0.2;

                    item.importanceScore = score;
                });

                // 按重要性排序
                finalItems.sort((a, b) => a.importanceScore - b.importanceScore);

                // 智能决定最终条目数量
                if (finalItems.length <= PREFERRED_ITEMS) {
                    // 条目数量合适，直接使用
                } else if (finalItems.length <= MAX_ITEMS) {
                    // 条目数量在可接受范围内，检查是否需要合并过短的条目
                    const shortItems = finalItems.filter(item => {
                        const contentLength = (item.content || '').length + (item.relatedLines || []).join('').length;
                        return contentLength < 30;
                    });

                    if (shortItems.length >= 2) {
                        // 合并过短的条目
                        const mergedShortItem = {
                            content: shortItems[0].content,
                            relatedLines: [
                                ...(shortItems[0].relatedLines || []),
                                shortItems[1].content,
                                ...(shortItems[1].relatedLines || [])
                            ],
                            priority: Math.min(shortItems[0].priority || 3, shortItems[1].priority || 3),
                            importanceScore: Math.min(shortItems[0].importanceScore, shortItems[1].importanceScore)
                        };

                        // 移除原来的短条目，添加合并后的条目
                        finalItems = finalItems.filter(item => !shortItems.slice(0, 2).includes(item));
                        finalItems.push(mergedShortItem);
                        finalItems.sort((a, b) => a.importanceScore - b.importanceScore);
                    }
                } else {
                    // 条目过多，需要合并
                    const keepItems = finalItems.slice(0, MAX_ITEMS - 1);
                    const mergeItems = finalItems.slice(MAX_ITEMS - 1);

                    if (mergeItems.length > 0) {
                        // 创建合并条目
                        const mergedContent = mergeItems.map(item => {
                            let content = item.content;
                            if (item.relatedLines && item.relatedLines.length > 0) {
                                content += '，' + item.relatedLines.join('，');
                            }
                            return content;
                        }).join('；');

                        keepItems.push({
                            content: '其他工作内容',
                            relatedLines: [mergedContent],
                            priority: 4,
                            importanceScore: 4
                        });
                    }

                    finalItems = keepItems;
                }

                // 生成最终的段落描述
                finalItems.forEach(item => {
                    if (item.content || item.relatedLines.length > 0) {
                        itemCounter++;

                        // 合并主要内容和相关信息为完整段落
                        let fullContent = item.content;
                        if (item.relatedLines.length > 0) {
                            // 智能连接相关信息
                            const relatedText = item.relatedLines.join('，');
                            if (fullContent) {
                                // 如果主要内容不以标点符号结尾，添加连接符
                                if (!fullContent.match(/[，。；：、]$/)) {
                                    fullContent += '，';
                                }
                                fullContent += relatedText;
                            } else {
                                fullContent = relatedText;
                            }
                        }

                        // 确保段落以分号结尾（商务报告风格）
                        if (fullContent && !fullContent.match(/[；。]$/)) {
                            fullContent += '；';
                        }

                        // 生成格式化的条目（简化格式标记）
                        processedLines.push(`
                            <div class="mb-4 leading-relaxed">
                                <span class="text-primary mr-2">${getNumberWithComma(itemCounter)}</span>
                                <span class="text-gray-800">${fullContent}</span>
                            </div>
                        `);
                    }
                });

                // 如果没有任何内容，处理原始行（智能限制）
                if (finalItems.length === 0 && lines.length > 0) {
                    const limitedLines = lines.slice(0, MAX_ITEMS);
                    limitedLines.forEach((line, index) => {
                        line = line.replace(/^\s*(?:[0-9一二三四五六七八九十]+[\.。、）\)：:]+\s*|[•\-\*]\s+|[（\(][0-9一二三四五六七八九十]+[）\)]\s*)/g, '').trim();
                        if (line) {
                            // 最终格式清理
                            line = cleanFormatMarks(line);

                            if (!line.match(/[；。]$/)) {
                                line += '；';
                            }
                            processedLines.push(`
                                <div class="mb-4 leading-relaxed">
                                    <span class="text-primary mr-2">${getNumberWithComma(index + 1)}</span>
                                    <span class="text-gray-800">${line}</span>
                                </div>
                            `);
                        }
                    });
                }

                return processedLines.join('');
            };

            // 如果没有明确分割，尝试智能分割
            if (nextStepsLines.length === 0) {
                // 寻找可能的分割点
                const allLines = summaryLines;
                const splitIndex = allLines.findIndex(line =>
                    line.match(/^(下一步|下周|后续|计划|接下来)/i)
                );

                if (splitIndex > 0) {
                    summaryLines = allLines.slice(0, splitIndex);
                    nextStepsLines = allLines.slice(splitIndex);
                }
            }

            // 生成合并的内容，包含两个部分
            const summaryContent = processContent(summaryLines);
            const nextStepsContent = processContent(nextStepsLines);

            // 创建合并的HTML内容
            let combinedContent = '';

            if (summaryContent) {
                combinedContent += `
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-primary mb-4 pb-2 border-b border-primary/20">
                            <i class="fa-solid fa-calendar-check mr-2"></i>本周工作总结
                        </h3>
                        ${summaryContent}
                    </div>
                `;
            }

            if (nextStepsContent) {
                // 为下一步计划生成总结段落
                const nextStepsSummary = generateNextStepsSummary(nextStepsLines);

                combinedContent += `
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-secondary mb-4 pb-2 border-b border-secondary/20">
                            <i class="fa-solid fa-list-check mr-2"></i>下一步计划
                        </h3>
                        ${nextStepsContent}
                        ${nextStepsSummary ? `
                            <div class="mt-6 pt-4 border-t border-gray-200">
                                <div class="bg-secondary/5 rounded-lg p-4">
                                    <h4 class="text-sm font-semibold text-secondary mb-2 flex items-center">
                                        <i class="fa-solid fa-bullseye mr-2"></i>整体规划概述
                                    </h4>
                                    <p class="text-gray-700 leading-relaxed text-sm">${nextStepsSummary}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }

            return {
                summary: summaryContent,
                nextSteps: nextStepsContent,
                combined: combinedContent
            };
        };

        // 单项目生成
        const generateSingleProject = async (projectId, projectName, workContent) => {
            // 调用AI API进行分析
            const result = await analyzeWithAI(workContent);

            if (result) {
                // 存储结果
                multiProjectResults[projectId] = {
                    name: projectName,
                    content: result.combined,
                    summary: result.summary,
                    nextSteps: result.nextSteps
                };

                // 更新合并的内容显示
                reportContent.innerHTML = result.combined || '';

                // 同时更新独立的区域（用于编辑功能）
                workSummary.innerHTML = result.summary || '';
                nextSteps.innerHTML = result.nextSteps || '';

                // 显示报告
                reportContainer.classList.remove('hidden');

                // 更新日期
                setCurrentDate();

                // 显示成功通知
                showSuccessNotification(`${projectName} 周报生成完成！`);
            }
        };

        // 批量生成所有项目
        const batchGenerateAllProjects = async () => {
            const projectGroups = document.querySelectorAll('.project-input-group');
            const projectsToGenerate = [];

            // 收集所有有内容的项目
            projectGroups.forEach(group => {
                const projectId = group.dataset.projectId;
                const projectName = group.querySelector('.project-name-input').value;
                const workContent = group.querySelector('.work-record-input').value.trim();

                if (workContent) {
                    projectsToGenerate.push({ projectId, projectName, workContent });
                }
            });

            if (projectsToGenerate.length === 0) {
                alert('请至少输入一个项目的工作记录');
                return;
            }

            // 显示批量处理状态
            processingStatus.classList.remove('hidden');
            reportContainer.classList.add('hidden');
            statusMessage.textContent = `正在批量生成 ${projectsToGenerate.length} 个项目的周报...`;

            try {
                // 逐个生成项目周报
                for (let i = 0; i < projectsToGenerate.length; i++) {
                    const { projectId, projectName, workContent } = projectsToGenerate[i];

                    statusMessage.textContent = `正在生成 ${projectName} (${i + 1}/${projectsToGenerate.length})...`;

                    const result = await analyzeWithAI(workContent);

                    if (result) {
                        multiProjectResults[projectId] = {
                            name: projectName,
                            content: result.combined,
                            summary: result.summary,
                            nextSteps: result.nextSteps
                        };
                    }
                }

                // 显示批量生成的结果
                displayMultiProjectResults();
                showSuccessNotification(`批量生成完成！共生成 ${projectsToGenerate.length} 个项目的周报。`);

            } catch (error) {
                console.error('批量生成失败:', error);
                showErrorNotification('批量生成过程中出现错误，请重试。');
            } finally {
                processingStatus.classList.add('hidden');
            }
        };

        // 显示多项目结果
        const displayMultiProjectResults = () => {
            const results = Object.values(multiProjectResults);
            if (results.length === 0) return;

            // 合并所有项目的内容
            let combinedContent = '';

            results.forEach((project, index) => {
                combinedContent += `
                    <div class="mb-8 ${index > 0 ? 'pt-6 border-t border-gray-200' : ''}">
                        <h3 class="text-xl font-bold text-primary mb-4 flex items-center">
                            <i class="fa-solid fa-building mr-2"></i>${project.name}
                        </h3>
                        ${project.content}
                    </div>
                `;
            });

            // 更新显示
            reportContent.innerHTML = combinedContent;
            reportContainer.classList.remove('hidden');
            setCurrentDate();
        };

        // 分析工作记录（单项目模式）
        const analyzeWorkRecord = async () => {
            const inputText = workRecordInput.value.trim();

            if (!inputText) {
                alert('请输入工作记录');
                return;
            }

            // 在分析前滚动到页面顶部
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });

            // 调用AI API进行分析
            const result = await analyzeWithAI(inputText);

            if (result) {
                // 更新合并的内容显示
                reportContent.innerHTML = result.combined || '';

                // 同时更新独立的区域（用于编辑功能）
                workSummary.innerHTML = result.summary || '';
                nextSteps.innerHTML = result.nextSteps || '';

                // 显示报告
                reportContainer.classList.remove('hidden');

                // 更新日期
                setCurrentDate();

                // 显示成功通知
                showSuccessNotification('工作周报生成完成！内容已按商务报告格式优化。');
            }
        };

        // 打开编辑模态框
        const openEditModal = (content, type) => {
            // 移除HTML标签获取纯文本
            const plainText = content.replace(/<\/?[^>]+>/gi, '');
            editContent.value = plainText;
            currentEditType = type;
            modalTitle.textContent = type === 'summary' ? '编辑工作总结' : '编辑下一步计划';
            editModal.classList.remove('hidden');
        };

        // 更新合并的内容显示
        const updateCombinedContent = () => {
            const summaryContent = workSummary.innerHTML;
            const nextStepsContent = nextSteps.innerHTML;

            let combinedContent = '';

            if (summaryContent) {
                combinedContent += `
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-primary mb-4 pb-2 border-b border-primary/20">
                            <i class="fa-solid fa-calendar-check mr-2"></i>本周工作总结
                        </h3>
                        ${summaryContent}
                    </div>
                `;
            }

            if (nextStepsContent) {
                // 为编辑后的下一步计划也生成总结段落
                const nextStepsLines = extractLinesFromHTML(nextStepsContent);
                const nextStepsSummary = generateNextStepsSummary(nextStepsLines);

                combinedContent += `
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-secondary mb-4 pb-2 border-b border-secondary/20">
                            <i class="fa-solid fa-list-check mr-2"></i>下一步计划
                        </h3>
                        ${nextStepsContent}
                        ${nextStepsSummary ? `
                            <div class="mt-6 pt-4 border-t border-gray-200">
                                <div class="bg-secondary/5 rounded-lg p-4">
                                    <h4 class="text-sm font-semibold text-secondary mb-2 flex items-center">
                                        <i class="fa-solid fa-bullseye mr-2"></i>整体规划概述
                                    </h4>
                                    <p class="text-gray-700 leading-relaxed text-sm">${nextStepsSummary}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }

            reportContent.innerHTML = combinedContent;
        };

        // 从HTML内容中提取文本行
        const extractLinesFromHTML = (htmlContent) => {
            if (!htmlContent) return [];

            // 创建临时DOM元素来解析HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            // 提取所有文本内容
            const textContent = tempDiv.textContent || tempDiv.innerText || '';

            // 分割成行并过滤空行
            return textContent.split('\n').filter(line => line.trim());
        };

        // 保存编辑内容
        const saveEditedContent = () => {
            const content = editContent.value.trim();

            if (!content) return;

            // 使用新的格式处理编辑后的内容
            const lines = content.split('\n').filter(line => line.trim());

            // 创建一个临时的解析结果
            const tempResult = parseContent(content);
            let processedContent;

            // 如果解析结果有内容，使用解析后的格式，否则使用简单的阿拉伯数字格式
            if (currentEditType === 'summary' && tempResult.summary) {
                processedContent = tempResult.summary;
            } else if (currentEditType === 'nextSteps' && tempResult.nextSteps) {
                processedContent = tempResult.nextSteps;
            } else {
                // 简单处理：使用新的阿拉伯数字格式
                processedContent = lines.map((item, idx) => {
                    // 清理内容并确保以分号结尾
                    let cleanedItem = item.replace(/^\s*(?:[0-9一二三四五六七八九十]+[\.。、）\)：:]+\s*|[•\-\*]\s+)/g, '').trim();
                    if (cleanedItem && !cleanedItem.match(/[；。]$/)) {
                        cleanedItem += '；';
                    }

                    return `
                        <div class="mb-4 leading-relaxed">
                            <span class="font-semibold text-primary mr-2">${getNumberWithComma(idx + 1)}</span>
                            <span class="text-gray-800">${cleanedItem}</span>
                        </div>
                    `;
                }).join('');
            }

            // 更新对应的内容区域
            if (currentEditType === 'summary') {
                workSummary.innerHTML = processedContent;
            } else {
                nextSteps.innerHTML = processedContent;
            }

            // 重新生成合并的内容显示
            updateCombinedContent();

            // 关闭模态框
            editModal.classList.add('hidden');
        };

        // 导出报告为文本文件
        const exportReport = () => {
            const reportText = reportContent.innerText;
            const dateText = reportDate.textContent;

            const content = `工作周报 - ${dateText}\n\n${reportText}`;

            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `工作周报_${dateText.replace(/[年月日]/g, '')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        };

        // 保存报告到本地存储
        const saveReport = () => {
            const reportData = {
                summary: workSummary.innerHTML,
                nextSteps: nextSteps.innerHTML,
                date: reportDate.textContent
            };

            localStorage.setItem('workReport', JSON.stringify(reportData));

            // 显示保存成功提示
            const saveNotification = document.createElement('div');
            saveNotification.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-y-0';
            saveNotification.innerHTML = '<i class="fa-solid fa-check-circle mr-2"></i> 报告已保存';
            document.body.appendChild(saveNotification);

            setTimeout(() => {
                saveNotification.classList.add('translate-y-16', 'opacity-0');
                setTimeout(() => saveNotification.remove(), 300);
            }, 2000);
        };

        // 加载已保存的报告和API设置
        const loadSavedData = () => {
            // 加载已保存的报告
            const reportData = localStorage.getItem('workReport');

            if (reportData) {
                try {
                    const data = JSON.parse(reportData);
                    if (data.summary && data.nextSteps) {
                        workSummary.innerHTML = data.summary;
                        nextSteps.innerHTML = data.nextSteps;
                        // 更新合并的内容显示
                        updateCombinedContent();
                    } else if (data.content) {
                        // 兼容旧格式
                        workSummary.innerHTML = data.content;
                        updateCombinedContent();
                    }
                    reportDate.textContent = data.date;

                    // 显示报告
                    reportContainer.classList.remove('hidden');
                } catch (e) {
                    console.error('Failed to load saved report:', e);
                }
            }

            // 加载选择的AI提供商
            const selectedProvider = localStorage.getItem('selectedAiProvider') || 'deepseek';
            aiProviderSelect.value = selectedProvider;

            // 更新UI
            updateProviderUI();
        };

        // 切换API Key显示/隐藏
        const toggleApiKeyVisibility = () => {
            // 切换输入框类型
            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                toggleApiKeyBtn.innerHTML = '<i class="fa-solid fa-eye"></i>';
            } else {
                apiKeyInput.type = 'password';
                toggleApiKeyBtn.innerHTML = '<i class="fa-solid fa-eye-slash"></i>';
            }
        };

        // 初始化事件监听
        const initEventListeners = () => {
            // 分析按钮
            analyzeBtn.addEventListener('click', analyzeWorkRecord);

            // 编辑总结按钮
            editSummaryBtn.addEventListener('click', () => {
                openEditModal(workSummary.innerHTML, 'summary');
            });

            // 编辑计划按钮
            editStepsBtn.addEventListener('click', () => {
                openEditModal(nextSteps.innerHTML, 'nextSteps');
            });

            // 重新生成按钮
            regenerateBtn.addEventListener('click', analyzeWorkRecord);

            // 移动端菜单按钮
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });

            // 滚动时改变导航栏样式
            window.addEventListener('scroll', () => {
                if (window.scrollY > 10) {
                    header.classList.add('shadow');
                    header.classList.add('py-2');
                    header.classList.remove('py-3');
                } else {
                    header.classList.remove('shadow');
                    header.classList.add('py-3');
                    header.classList.remove('py-2');
                }
            });

            // 保存按钮
            saveBtn.addEventListener('click', saveReport);

            // 导出按钮
            exportBtn.addEventListener('click', exportReport);

            // 关闭模态框按钮
            closeModalBtn.addEventListener('click', () => {
                editModal.classList.add('hidden');
            });

            // 取消编辑按钮
            cancelEditBtn.addEventListener('click', () => {
                editModal.classList.add('hidden');
            });

            // 保存编辑按钮
            saveEditBtn.addEventListener('click', saveEditedContent);

            // 点击模态框外部关闭
            editModal.addEventListener('click', (e) => {
                if (e.target === editModal) {
                    editModal.classList.add('hidden');
                }
            });

            // API设置相关事件
            apiKeyBtn.addEventListener('click', () => {
                document.querySelector('html').scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // 切换API Key显示/隐藏
            toggleApiKeyBtn.addEventListener('click', toggleApiKeyVisibility);

            // 保存API Key按钮
            saveApiKeyBtn.addEventListener('click', saveApiKey);

            // 测试API连接按钮
            testApiKeyBtn.addEventListener('click', () => testApiConnection());

            // AI模型选择变化
            aiProviderSelect.addEventListener('change', updateProviderUI);

            // API Key输入实时验证
            apiKeyInput.addEventListener('input', () => {
                const apiKey = apiKeyInput.value;
                if (apiKey.length > 5) { // 只在输入了一定长度后才验证
                    const validation = validateApiKey(apiKey);
                    if (!validation.valid) {
                        // 显示实时错误提示，但不太显眼
                        apiKeyError.querySelector('span').textContent = validation.error;
                        apiKeyError.classList.remove('hidden');
                        apiKeySuccess.classList.add('hidden');
                    } else {
                        // 清除错误提示
                        apiKeyError.classList.add('hidden');
                        // 不自动显示成功，避免干扰用户输入
                    }
                }
            });

            // API Key输入失去焦点时清理
            apiKeyInput.addEventListener('blur', () => {
                const apiKey = apiKeyInput.value;
                if (apiKey) {
                    const validation = validateApiKey(apiKey);
                    if (validation.valid) {
                        // 自动清理API Key
                        apiKeyInput.value = validation.cleanedKey;
                        apiKeyError.classList.add('hidden');
                    }
                }
            });

            // 多项目相关事件监听器
            addProjectBtn.addEventListener('click', addNewProject);
            batchGenerateBtn.addEventListener('click', batchGenerateAllProjects);
            clearAllBtn.addEventListener('click', clearAllProjects);

            // 初始化项目事件监听器
            setupProjectEventListeners();

            // 为工作总结和下一步计划区域添加精确选择功能
            setupContentSelection();
        };

        // 设置内容选择功能
        const setupContentSelection = () => {
            // 为合并的报告内容区域添加选择功能
            reportContent.addEventListener('focus', () => {
                reportContent.classList.add('ring-2', 'ring-primary/50');
            });

            reportContent.addEventListener('blur', () => {
                reportContent.classList.remove('ring-2', 'ring-primary/50');
            });

            // 添加键盘快捷键支持（Cmd+A / Ctrl+A）
            const handleSelectAll = (element, event) => {
                if ((event.metaKey || event.ctrlKey) && event.key === 'a') {
                    event.preventDefault();
                    selectElementContent(element);
                }
            };

            reportContent.addEventListener('keydown', (e) => handleSelectAll(reportContent, e));

            // 点击时自动聚焦
            reportContent.addEventListener('click', () => {
                reportContent.focus();
            });
        };

        // 选择元素内容的函数
        const selectElementContent = (element) => {
            if (window.getSelection && document.createRange) {
                const selection = window.getSelection();
                const range = document.createRange();
                range.selectNodeContents(element);
                selection.removeAllRanges();
                selection.addRange(range);
            } else if (document.body.createTextRange) {
                // IE支持
                const range = document.body.createTextRange();
                range.moveToElementText(element);
                range.select();
            }
        };

        // 初始化应用
        const initApp = () => {
            initEventListeners();
            loadSavedData();
            updateProviderUI();
        };

        // 启动应用
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>

