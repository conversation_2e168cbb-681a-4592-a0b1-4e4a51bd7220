// Jason's Meeting Note - 元素检查工具
// 这个脚本可以在主应用页面的控制台中运行，用于检查所有必需的HTML元素

function checkMeetingNoteElements() {
    console.log('🔍 开始检查 Jason\'s Meeting Note 应用元素...\n');
    
    const requiredElements = {
        // AI设置相关元素
        'AI设置': {
            'toggleAISettings': 'AI设置切换按钮',
            'aiSettingsPanel': 'AI设置面板',
            'aiProvider': 'AI服务商选择',
            'apiKey': 'API Key输入框',
            'apiBaseUrl': 'API Base URL输入框',
            'modelName': '模型名称选择',
            'temperature': '温度滑块',
            'maxTokens': '最大Token数输入框',
            'customPrompt': '自定义Prompt文本框',
            'toggleApiKey': 'API Key显示切换按钮',
            'testConnection': '测试连接按钮',
            'saveAIConfig': '保存AI配置按钮',
            'clearAIConfig': '清空AI配置按钮',
            'connectionStatus': '连接状态区域',
            'statusIndicator': '状态指示器',
            'statusMessage': '状态消息'
        },
        
        // 语音识别相关元素
        '语音识别': {
            'startRecording': '开始录音按钮',
            'stopRecording': '停止录音按钮',
            'pauseRecording': '暂停录音按钮',
            'recordingStatus': '录音状态指示器',
            'transcriptionText': '语音转录显示区域',
            'clearTranscription': '清空转录按钮'
        },
        
        // 手动输入相关元素
        '手动输入': {
            'manualNotes': '手动笔记文本框',
            'clearManualNotes': '清空手动笔记按钮',
            'addTimestamp': '添加时间戳按钮'
        },
        
        // 会议信息相关元素
        '会议信息': {
            'meetingTitle': '会议主题输入框',
            'meetingTime': '会议时间输入框',
            'participants': '参会人员输入框'
        },
        
        // 纪要生成相关元素
        '纪要生成': {
            'summaryMode': '纪要模式选择',
            'generateSummary': '生成纪要按钮',
            'meetingSummary': '会议纪要显示区域',
            'aiProcessingStatus': 'AI处理状态区域',
            'processingMessage': '处理消息',
            'progressFill': '进度条填充'
        },
        
        // 操作按钮相关元素
        '操作按钮': {
            'saveMeeting': '保存会议按钮',
            'exportMeeting': '导出会议按钮',
            'newMeeting': '新建会议按钮',
            'loadMeeting': '加载会议按钮'
        },
        
        // 其他界面元素
        '其他界面': {
            'statusModal': '状态模态框',
            'modalMessage': '模态框消息',
            'historyList': '历史记录列表'
        }
    };
    
    let totalElements = 0;
    let foundElements = 0;
    let missingElements = [];
    
    // 检查每个分类的元素
    Object.keys(requiredElements).forEach(category => {
        console.log(`\n📂 检查 ${category} 相关元素:`);
        
        const elements = requiredElements[category];
        Object.keys(elements).forEach(elementId => {
            totalElements++;
            const element = document.getElementById(elementId);
            const description = elements[elementId];
            
            if (element) {
                console.log(`  ✅ ${elementId} (${description}): 找到`);
                foundElements++;
                
                // 检查元素的基本属性
                const info = {
                    tagName: element.tagName,
                    className: element.className,
                    visible: element.offsetHeight > 0,
                    disabled: element.disabled
                };
                
                if (info.disabled) {
                    console.log(`    ⚠️  元素被禁用`);
                }
                if (!info.visible) {
                    console.log(`    ℹ️  元素不可见 (可能是正常的隐藏状态)`);
                }
            } else {
                console.log(`  ❌ ${elementId} (${description}): 未找到`);
                missingElements.push({
                    id: elementId,
                    description: description,
                    category: category
                });
            }
        });
    });
    
    // 输出总结
    console.log(`\n📊 检查结果总结:`);
    console.log(`总元素数: ${totalElements}`);
    console.log(`找到元素: ${foundElements}`);
    console.log(`缺失元素: ${missingElements.length}`);
    console.log(`完整性: ${((foundElements / totalElements) * 100).toFixed(1)}%`);
    
    if (missingElements.length > 0) {
        console.log(`\n❌ 缺失的元素列表:`);
        missingElements.forEach(item => {
            console.log(`  • ${item.id} (${item.description}) - 分类: ${item.category}`);
        });
        
        console.log(`\n🔧 修复建议:`);
        console.log(`1. 检查 HTML 文件中是否包含上述缺失的元素`);
        console.log(`2. 确认元素的 ID 属性拼写正确`);
        console.log(`3. 检查是否有 JavaScript 错误阻止了元素的创建`);
        console.log(`4. 确认页面完全加载完成后再运行此检查`);
    } else {
        console.log(`\n🎉 所有必需元素都已找到！应用应该能正常工作。`);
    }
    
    return {
        total: totalElements,
        found: foundElements,
        missing: missingElements,
        completeness: (foundElements / totalElements) * 100
    };
}

// 检查事件监听器绑定
function checkEventListeners() {
    console.log('\n🔗 检查事件监听器绑定...');
    
    const buttonElements = [
        'toggleAISettings',
        'startRecording', 
        'stopRecording',
        'pauseRecording',
        'generateSummary',
        'testConnection'
    ];
    
    buttonElements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            // 检查是否有点击事件监听器
            const hasClickListener = element.onclick !== null || 
                                   getEventListeners(element).click?.length > 0;
            
            console.log(`  ${hasClickListener ? '✅' : '❌'} ${elementId}: ${hasClickListener ? '已绑定事件' : '未绑定事件'}`);
        }
    });
}

// 检查应用初始化状态
function checkAppInitialization() {
    console.log('\n🚀 检查应用初始化状态...');
    
    // 检查全局变量
    if (typeof window.meetingApp !== 'undefined' && window.meetingApp !== null) {
        console.log('  ✅ window.meetingApp: 已初始化');
        
        // 检查应用实例的关键属性
        const app = window.meetingApp;
        console.log(`  📊 应用状态:`);
        console.log(`    - 录音状态: ${app.isRecording ? '录音中' : '未录音'}`);
        console.log(`    - 暂停状态: ${app.isPaused ? '已暂停' : '未暂停'}`);
        console.log(`    - AI配置: ${app.aiConfig ? '已配置' : '未配置'}`);
        
    } else {
        console.log('  ❌ window.meetingApp: 未初始化或初始化失败');
        console.log('  🔧 建议: 刷新页面或检查 JavaScript 错误');
    }
    
    // 检查是否有 JavaScript 错误
    if (window.console && console.error) {
        console.log('  ℹ️  如有 JavaScript 错误，请查看控制台的错误信息');
    }
}

// 运行完整检查
function runFullCheck() {
    console.clear();
    console.log('🔍 Jason\'s Meeting Note - 完整元素和功能检查\n');
    console.log('=' .repeat(60));
    
    const elementCheck = checkMeetingNoteElements();
    checkEventListeners();
    checkAppInitialization();
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 检查完成！');
    
    if (elementCheck.completeness === 100) {
        console.log('🎉 所有检查都通过了！应用应该能正常工作。');
    } else {
        console.log('⚠️  发现一些问题，请根据上述建议进行修复。');
    }
    
    return elementCheck;
}

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        checkMeetingNoteElements,
        checkEventListeners,
        checkAppInitialization,
        runFullCheck
    };
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.MeetingNoteChecker = {
        checkElements: checkMeetingNoteElements,
        checkListeners: checkEventListeners,
        checkApp: checkAppInitialization,
        runFullCheck: runFullCheck
    };
    
    console.log('🛠️  元素检查工具已加载！');
    console.log('使用方法:');
    console.log('  • runFullCheck() - 运行完整检查');
    console.log('  • checkMeetingNoteElements() - 只检查元素');
    console.log('  • checkEventListeners() - 只检查事件监听器');
    console.log('  • checkAppInitialization() - 只检查应用初始化');
}
