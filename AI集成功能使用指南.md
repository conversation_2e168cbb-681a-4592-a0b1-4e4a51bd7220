# Jason's Meeting Note - AI大模型集成功能使用指南

## 🎯 功能概述

Jason's Meeting Note现已集成多个主流AI大模型API，为您的会议记录提供智能化的纪要生成服务。支持的AI服务商包括：

- **DeepSeek API** - 高性能中文理解模型
- **MiniMax API** - 国产优秀对话模型  
- **豆包(字节跳动) API** - 字节跳动智能助手
- **Google Gemini API** - Google最新多模态模型
- **OpenAI GPT API** - 业界领先的GPT系列模型
- **自定义API** - 支持其他兼容OpenAI格式的API

## 🚀 快速开始

### 1. 配置AI设置

1. **打开AI设置面板**
   - 点击页面上方的"配置AI"按钮
   - 设置面板将展开显示配置选项

2. **选择AI服务商**
   - 从下拉菜单中选择您要使用的AI服务商
   - 系统会自动填充对应的默认配置

3. **填写API配置**
   - **API Key**: 输入您的API密钥（支持显示/隐藏切换）
   - **API Base URL**: 确认或修改API基础地址
   - **模型名称**: 选择要使用的具体模型
   - **Temperature**: 调整AI创造性（0-1，默认0.7）
   - **Max Tokens**: 设置最大输出长度（默认2000）

4. **测试连接**
   - 点击"测试连接"按钮验证配置
   - 查看连接状态指示器：
     - 🟢 绿色 = 连接成功
     - 🔴 红色 = 连接失败  
     - 🟡 黄色 = 测试中

5. **保存配置**
   - 测试成功后点击"保存配置"
   - 配置将安全保存到本地存储

### 2. 使用AI生成会议纪要

1. **录制或输入会议内容**
   - 使用语音识别功能录制会议
   - 或在手动笔记区域输入关键信息

2. **选择生成模式**
   - **基础整理**: 不使用AI，简单格式化
   - **AI简洁版**: AI提取关键信息，简洁呈现
   - **AI详细版**: AI生成详细完整的会议纪要
   - **AI行动项提取**: AI专门提取任务和行动项

3. **生成纪要**
   - 点击"生成纪要"按钮
   - AI处理过程中会显示进度指示
   - 生成完成后查看智能纪要结果

## 📋 各AI服务商配置说明

### DeepSeek API
```
Base URL: https://api.deepseek.com/v1
推荐模型: deepseek-chat, deepseek-coder
特点: 中文理解能力强，代码生成优秀
```

### MiniMax API  
```
Base URL: https://api.minimax.chat/v1
推荐模型: abab6.5s-chat, abab6.5-chat
特点: 国产模型，中文对话自然
```

### 豆包 API
```
Base URL: https://ark.cn-beijing.volces.com/api/v3
模型: 需要使用您的端点ID
特点: 字节跳动出品，理解能力强
```

### Google Gemini API
```
Base URL: https://generativelanguage.googleapis.com/v1beta
推荐模型: gemini-1.5-pro, gemini-1.5-flash
特点: 多模态能力，推理能力强
```

### OpenAI GPT API
```
Base URL: https://api.openai.com/v1
推荐模型: gpt-4o, gpt-4o-mini, gpt-3.5-turbo
特点: 业界标杆，综合能力最强
```

## 🎨 自定义Prompt模板

您可以在AI设置中自定义Prompt模板，指导AI按照您的需求生成纪要：

### 示例模板

**会议纪要模板**:
```
请将以下会议内容整理成专业的会议纪要，包含：
1. 会议概述
2. 主要讨论点
3. 重要决策
4. 行动项和负责人
5. 下次会议安排
```

**项目会议模板**:
```
请按照项目管理的角度整理会议内容：
1. 项目进展汇报
2. 遇到的问题和挑战
3. 解决方案和决策
4. 资源需求
5. 时间节点和里程碑
```

## ⚙️ 高级设置

### Temperature参数说明
- **0.0-0.3**: 输出更加确定和一致，适合事实性内容
- **0.4-0.7**: 平衡创造性和准确性，适合大多数场景
- **0.8-1.0**: 更有创造性，适合需要多样化表达的场景

### Max Tokens设置
- **500-1000**: 适合简短纪要
- **1000-2000**: 适合标准会议纪要
- **2000-4000**: 适合详细会议记录
- **4000+**: 适合长时间会议或复杂讨论

## 🔒 安全和隐私

### 数据安全
- 所有API配置保存在本地浏览器存储
- API Key采用安全存储，支持显示/隐藏切换
- 会议内容仅在生成纪要时发送给AI服务商
- 不会永久存储您的会议内容在第三方服务器

### 隐私保护
- 建议使用企业级API账户处理敏感会议
- 可以选择基础整理模式避免使用AI
- 支持完全离线的基础功能

## 🛠️ 故障排除

### 常见问题

**1. 连接测试失败**
- 检查API Key是否正确
- 确认网络连接正常
- 验证API Base URL是否正确
- 检查API账户余额和权限

**2. AI生成失败**
- 检查API配额是否充足
- 确认模型名称是否正确
- 尝试降低Max Tokens设置
- 检查会议内容是否过长

**3. 响应速度慢**
- 选择更快的模型（如GPT-3.5-turbo）
- 减少Max Tokens设置
- 检查网络连接质量
- 尝试不同的API服务商

### 错误代码说明
- **401**: API Key无效或已过期
- **403**: API访问被拒绝，检查权限
- **429**: API调用频率超限，稍后重试
- **500**: API服务器错误，稍后重试

## 💡 使用技巧

### 最佳实践
1. **选择合适的模型**: 根据会议类型选择最适合的AI模型
2. **优化Prompt**: 根据您的需求自定义Prompt模板
3. **分段处理**: 对于很长的会议，可以分段生成纪要
4. **备份配置**: 定期导出AI配置进行备份
5. **测试对比**: 尝试不同模型和参数找到最佳效果

### 效率提升
- 使用快捷键和预设模板
- 建立标准化的会议记录流程
- 结合语音识别和手动笔记
- 定期更新和优化AI配置

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 查看浏览器控制台的错误信息
2. 检查API服务商的官方文档
3. 尝试不同的配置参数
4. 联系相应API服务商的技术支持

---

**版本**: v2.0.0  
**更新时间**: 2024-12-19  
**兼容性**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
