/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    margin-bottom: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #e9ecef;
}

.header h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 700;
}

.header h1 i {
    color: #3498db;
    margin-right: 15px;
}

.subtitle {
    color: #7f8c8d;
    font-size: 1.1em;
    font-weight: 300;
}

/* 会议信息区域 */
.meeting-info {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #3498db;
}

.info-row {
    display: grid;
    grid-template-columns: 2fr 1fr 2fr;
    gap: 15px;
}

.input-field {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.input-field:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* 语音识别区域 */
.voice-section {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.voice-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.transcription-area h3,
.manual-input-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2em;
    display: flex;
    align-items: center;
}

.transcription-area h3 i,
.manual-input-section h3 i {
    margin-right: 10px;
    color: #3498db;
}

.text-display {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
    max-height: 300px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 15px;
}

.placeholder-text {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    margin-top: 80px;
}

/* 手动输入区域 */
.manual-input-section {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.manual-textarea {
    width: 100%;
    min-height: 150px;
    padding: 12px;
    border: 1px solid #ced4da;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.manual-textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

/* 状态指示器样式 */
.status-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    margin-left: 10px;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
    color: #6c757d;
}

.status-recording {
    background-color: #dc3545;
    color: white;
    animation: pulse 1.5s infinite;
}

.status-paused {
    background-color: #ffc107;
    color: #212529;
}

.status-success {
    background-color: #28a745;
    color: white;
}

.status-error {
    background-color: #dc3545;
    color: white;
}

.status-info {
    background-color: #17a2b8;
    color: white;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 临时识别文本样式 */
.interim-text {
    color: #6c757d;
    font-style: italic;
}

/* 转录内容样式 */
.transcription-content {
    white-space: pre-wrap;
    line-height: 1.5;
}

/* 控制按钮组 */
.transcription-controls,
.input-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 会议纪要区域 */
.summary-section {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    margin-bottom: 30px;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.summary-header h3 {
    color: #2c3e50;
    font-size: 1.3em;
    display: flex;
    align-items: center;
}

.summary-header h3 i {
    margin-right: 10px;
    color: #27ae60;
}

.summary-display {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
    font-size: 14px;
    line-height: 1.8;
}

/* 操作按钮区域 */
.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

/* 历史记录区域 */
.history-section {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.history-list {
    max-height: 300px;
    overflow-y: auto;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* AI设置区域样式 */
.ai-settings-section {
    background: #fff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    margin-bottom: 30px;
}

.ai-settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.ai-settings-header h3 {
    color: #2c3e50;
    font-size: 1.2em;
    display: flex;
    align-items: center;
    margin: 0;
}

.ai-settings-header h3 i {
    margin-right: 10px;
    color: #9b59b6;
}

.ai-settings-panel {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.setting-select, .setting-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.setting-select:focus, .setting-input:focus {
    border-color: #4a89dc;
    outline: none;
    box-shadow: 0 0 0 3px rgba(74, 137, 220, 0.1);
}

.setting-textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: all 0.3s ease;
}

.setting-textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.password-input-group {
    display: flex;
    align-items: center;
}

.password-input-group .setting-input {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.password-input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border: 1px solid #ced4da;
    border-left: none;
    background: #f8f9fa;
}

/* 验证消息样式 */
.validation-message {
    margin-top: 5px;
    font-size: 12px;
    transition: all 0.2s;
}

.valid-message {
    color: #28a745;
}

.invalid-message {
    color: #dc3545;
}

/* Toast消息样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 1000;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.toast-info {
    background-color: #17a2b8;
}

.toast-success {
    background-color: #28a745;
}

.toast-warning {
    background-color: #ffc107;
    color: #212529;
}

.toast-error {
    background-color: #dc3545;
}

.toast i {
    margin-right: 8px;
}

/* 会议纪要控制区域 */
.summary-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.summary-controls .setting-select {
    width: auto;
    min-width: 150px;
}

/* AI处理状态 */
.ai-processing-status {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.processing-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #3498db;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 3px;
    width: 0%;
    transition: width 0.3s ease;
    animation: progressAnimation 2s infinite;
}

@keyframes progressAnimation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* AI增强纪要样式 */
.ai-enhanced {
    position: relative;
}

.ai-badge {
    position: absolute;
    top: -10px;
    right: 10px;
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(155, 89, 182, 0.3);
}

.ai-badge i {
    margin-right: 5px;
}

/* 折叠详情样式 */
details {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 10px;
}

details[open] {
    background: #f8f9fa;
}

details summary {
    padding: 5px;
    outline: none;
    user-select: none;
}

details summary:hover {
    background: rgba(52, 152, 219, 0.1);
    border-radius: 4px;
}

/* 调试面板样式 */
.debug-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    max-height: 80vh;
    background: white;
    border: 2px solid #007bff;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    overflow: hidden;
}

.debug-header {
    background: #007bff;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.debug-header h4 {
    margin: 0;
    font-size: 14px;
}

.debug-content {
    padding: 15px;
    max-height: 60vh;
    overflow-y: auto;
}

.debug-section {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.debug-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.debug-section h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.debug-section div {
    font-size: 11px;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    white-space: pre-wrap;
    max-height: 120px;
    overflow-y: auto;
}

/* API配置区域 */
.api-config-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin-top: 15px;
}

/* 按钮尺寸变体 */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* 音频源选择器样式 */
.audio-source-selector {
    margin-bottom: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.audio-source-selector h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #2c3e50;
}

.radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
}

.radio-label {
    display: flex;
    flex-direction: column;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    flex: 1;
    min-width: 150px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.radio-label:hover {
    background-color: #e9ecef;
}

.radio-label input[type="radio"] {
    margin-right: 8px;
}

.radio-label input[type="radio"]:checked + span {
    font-weight: bold;
    color: #2c3e50;
}

.radio-label small {
    margin-top: 5px;
    color: #6c757d;
    font-size: 0.8em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .info-row {
        grid-template-columns: 1fr;
    }

    .voice-controls {
        justify-content: center;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .summary-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .summary-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .ai-settings-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .connection-test {
        flex-direction: column;
        align-items: stretch;
    }

    .password-input-group {
        flex-direction: column;
    }
}
