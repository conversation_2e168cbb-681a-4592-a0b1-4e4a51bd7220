# Jason's Meeting Note - 智能会议记录系统

## 项目简介

Jason's Meeting Note 是一个基于Web技术的智能会议记录应用，专为提高会议效率和记录质量而设计。该应用结合了实时语音转文字技术和手动笔记功能，能够自动生成结构化的会议纪要。

## 核心功能

### 🎤 实时语音转录
- 使用Web Speech API实现实时语音识别
- 支持中文语音转录（可扩展其他语言）
- 提供录音控制：开始、暂停、停止
- 实时显示转录结果和临时识别内容

### ✏️ 手动笔记输入
- 独立的文本输入区域
- 支持时间戳插入功能
- 可记录个人想法、关键决策、行动项等
- 与语音转录内容互补

### 📋 智能纪要生成
- 自动整合语音转录和手动笔记
- 生成结构化的会议纪要
- 包含会议基本信息、参会人员、内容摘要
- 支持HTML格式导出

### 💾 数据管理
- 本地存储会议记录
- 历史记录查看和加载
- 会议记录导出功能
- 支持新建会议和数据清理

## 技术栈

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **语音识别**: Web Speech API
- **数据存储**: localStorage (浏览器本地存储)
- **样式框架**: 自定义CSS + Font Awesome图标
- **兼容性**: 现代浏览器 (Chrome, Edge, Safari)

## 文件结构

```
Jason's Meeting Note/
├── meeting-notes.html    # 主HTML文件
├── styles.css           # 样式文件
├── script.js           # JavaScript功能文件
└── README.md           # 项目说明文档
```

## 快速开始

### 1. 环境要求
- 现代浏览器（推荐Chrome、Edge、Safari）
- 麦克风设备
- 网络连接（用于加载Font Awesome图标）

### 2. 安装步骤
1. 下载所有项目文件到本地目录
2. 使用浏览器打开 `meeting-notes.html` 文件
3. 允许浏览器访问麦克风权限
4. 开始使用！

### 3. 基本使用流程
1. **设置会议信息**：填写会议主题、时间、参会人员
2. **开始录音**：点击"开始录音"按钮，开始语音转录
3. **手动记录**：在笔记区域输入重要信息和个人想法
4. **生成纪要**：点击"生成纪要"按钮，自动整合所有内容
5. **保存导出**：保存到本地存储或导出为HTML文档

## 功能详解

### 语音识别功能
- **开始录音**: 启动语音识别，实时转录语音内容
- **暂停/继续**: 临时暂停录音，可随时继续
- **停止录音**: 结束语音识别会话
- **清空转录**: 清除当前转录内容

### 手动笔记功能
- **文本输入**: 大文本区域，支持多行输入
- **时间戳**: 自动插入当前时间标记
- **清空笔记**: 快速清除手动输入内容

### 会议纪要生成
- **智能整合**: 自动合并语音转录和手动笔记
- **结构化输出**: 按照标准格式生成会议纪要
- **实时预览**: 即时查看生成的纪要内容

### 数据管理
- **本地保存**: 会议记录保存在浏览器本地存储
- **历史查看**: 查看和加载历史会议记录
- **导出功能**: 将纪要导出为HTML文档
- **新建会议**: 快速清空当前内容，开始新会议

## 浏览器兼容性

| 浏览器 | 版本要求 | 语音识别支持 |
|--------|----------|-------------|
| Chrome | 25+ | ✅ 完全支持 |
| Edge | 79+ | ✅ 完全支持 |
| Safari | 14.1+ | ✅ 完全支持 |
| Firefox | - | ❌ 不支持 |

## 使用技巧

### 1. 语音识别优化
- 确保环境相对安静
- 说话清晰，语速适中
- 定期暂停让系统处理识别结果
- 重要内容可重复说明

### 2. 手动笔记建议
- 记录语音识别可能遗漏的信息
- 标注重要决策和行动项
- 使用时间戳标记关键时刻
- 补充参会人员的具体观点

### 3. 纪要生成最佳实践
- 在会议进行中定期生成纪要预览
- 结合语音转录和手动笔记的优势
- 会议结束后及时保存和导出
- 为重要会议添加详细的标题和参会人员信息

## 故障排除

### 常见问题

**Q: 语音识别不工作？**
A: 检查浏览器是否支持Web Speech API，确认麦克风权限已授予，尝试刷新页面。

**Q: 识别准确率不高？**
A: 确保环境安静，说话清晰，可以尝试调整麦克风位置或音量。

**Q: 无法保存会议记录？**
A: 检查浏览器是否启用了localStorage，清理浏览器缓存后重试。

**Q: 导出功能不工作？**
A: 确保浏览器支持Blob和URL.createObjectURL API，检查是否有弹窗拦截。

### 技术支持

如遇到技术问题，请检查：
1. 浏览器控制台是否有错误信息
2. 网络连接是否正常
3. 浏览器版本是否符合要求
4. 麦克风设备是否正常工作

## 未来规划

### 计划功能
- [ ] 多语言支持
- [ ] 云端存储集成
- [ ] 会议录音功能
- [ ] AI智能摘要
- [ ] 团队协作功能
- [ ] 移动端适配
- [ ] 与视频会议平台集成

### 技术改进
- [ ] 离线语音识别
- [ ] 更好的语音识别准确率
- [ ] 实时协作编辑
- [ ] 数据加密存储
- [ ] 性能优化

## 开发者信息

- **项目名称**: Jason's Meeting Note
- **开发者**: Jason
- **技术栈**: HTML5 + CSS3 + JavaScript
- **开源协议**: MIT License

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 基础语音识别功能
- ✅ 手动笔记输入
- ✅ 会议纪要生成
- ✅ 本地存储和导出
- ✅ 响应式设计
- ✅ 基础UI/UX设计

---

**感谢使用 Jason's Meeting Note！** 🎉

如有任何建议或问题，欢迎反馈。让我们一起让会议更高效！
