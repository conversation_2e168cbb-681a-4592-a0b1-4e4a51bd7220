<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>Jason's Meeting Note - 音频功能测试</h1>
    
    <div class="test-section">
        <h2>1. 浏览器兼容性检测</h2>
        <button class="test-button" onclick="testBrowserSupport()">检测浏览器支持</button>
        <div id="browserResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>2. 麦克风访问测试</h2>
        <button class="test-button" onclick="testMicrophone()">测试麦克风</button>
        <button class="test-button" onclick="stopMicrophone()" disabled id="stopMicBtn">停止麦克风</button>
        <div id="micResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>3. 系统音频捕获测试</h2>
        <button class="test-button" onclick="testSystemAudio()">测试系统音频</button>
        <button class="test-button" onclick="stopSystemAudio()" disabled id="stopSysBtn">停止系统音频</button>
        <div id="sysResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>4. 语音识别测试</h2>
        <button class="test-button" onclick="testSpeechRecognition()">测试语音识别</button>
        <button class="test-button" onclick="stopSpeechRecognition()" disabled id="stopSpeechBtn">停止识别</button>
        <div id="speechResult" class="result" style="display: none;"></div>
        <div id="transcription" style="margin-top: 10px; padding: 10px; background: #e9ecef; border-radius: 4px; min-height: 50px;"></div>
    </div>

    <script>
        let micStream = null;
        let sysStream = null;
        let recognition = null;

        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = isSuccess ? 'result success' : 'result error';
            element.style.display = 'block';
        }

        function testBrowserSupport() {
            const results = [];
            
            // 检测语音识别
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                results.push('✅ 语音识别 API 支持');
            } else {
                results.push('❌ 语音识别 API 不支持');
            }
            
            // 检测getUserMedia
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                results.push('✅ 麦克风访问 API 支持');
            } else {
                results.push('❌ 麦克风访问 API 不支持');
            }
            
            // 检测getDisplayMedia
            if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                results.push('✅ 屏幕共享 API 支持');
            } else {
                results.push('❌ 屏幕共享 API 不支持');
            }
            
            // 检测MediaRecorder
            if (window.MediaRecorder) {
                results.push('✅ 媒体录制 API 支持');
            } else {
                results.push('❌ 媒体录制 API 不支持');
            }
            
            // 检测AudioContext
            if (window.AudioContext || window.webkitAudioContext) {
                results.push('✅ 音频处理 API 支持');
            } else {
                results.push('❌ 音频处理 API 不支持');
            }
            
            showResult('browserResult', results.join('<br>'));
        }

        async function testMicrophone() {
            try {
                micStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                showResult('micResult', '✅ 麦克风访问成功！音频流已获取。');
                document.getElementById('stopMicBtn').disabled = false;
            } catch (error) {
                showResult('micResult', `❌ 麦克风访问失败: ${error.message}`, false);
            }
        }

        function stopMicrophone() {
            if (micStream) {
                micStream.getTracks().forEach(track => track.stop());
                micStream = null;
                showResult('micResult', '🛑 麦克风已停止');
                document.getElementById('stopMicBtn').disabled = true;
            }
        }

        async function testSystemAudio() {
            try {
                sysStream = await navigator.mediaDevices.getDisplayMedia({ 
                    video: false, 
                    audio: true 
                });
                showResult('sysResult', '✅ 系统音频捕获成功！请确保在屏幕共享时选择了"共享音频"选项。');
                document.getElementById('stopSysBtn').disabled = false;
            } catch (error) {
                showResult('sysResult', `❌ 系统音频捕获失败: ${error.message}`, false);
            }
        }

        function stopSystemAudio() {
            if (sysStream) {
                sysStream.getTracks().forEach(track => track.stop());
                sysStream = null;
                showResult('sysResult', '🛑 系统音频已停止');
                document.getElementById('stopSysBtn').disabled = true;
            }
        }

        function testSpeechRecognition() {
            try {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();
                
                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = 'zh-CN';
                
                recognition.onstart = () => {
                    showResult('speechResult', '✅ 语音识别已启动，请开始说话...');
                    document.getElementById('stopSpeechBtn').disabled = false;
                    document.getElementById('transcription').innerHTML = '<em>等待语音输入...</em>';
                };
                
                recognition.onresult = (event) => {
                    let transcript = '';
                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        transcript += event.results[i][0].transcript;
                    }
                    document.getElementById('transcription').innerHTML = transcript || '<em>等待语音输入...</em>';
                };
                
                recognition.onerror = (event) => {
                    showResult('speechResult', `❌ 语音识别错误: ${event.error}`, false);
                };
                
                recognition.onend = () => {
                    showResult('speechResult', '🛑 语音识别已停止');
                    document.getElementById('stopSpeechBtn').disabled = true;
                };
                
                recognition.start();
                
            } catch (error) {
                showResult('speechResult', `❌ 语音识别启动失败: ${error.message}`, false);
            }
        }

        function stopSpeechRecognition() {
            if (recognition) {
                recognition.stop();
                recognition = null;
            }
        }

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            stopMicrophone();
            stopSystemAudio();
            stopSpeechRecognition();
        });
    </script>
</body>
</html>
