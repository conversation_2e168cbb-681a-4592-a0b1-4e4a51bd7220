<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>格式优化对比 - 工作周报AI编辑器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', system-ui, sans-serif;
        }
        .demo-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 标题区域 -->
    <div class="demo-section py-12">
        <div class="max-w-6xl mx-auto px-6 text-center">
            <h1 class="text-4xl font-bold mb-4">工作周报AI编辑器</h1>
            <h2 class="text-2xl font-light mb-6">格式优化对比演示</h2>
            <p class="text-lg opacity-90">从层级结构到商务报告格式的全面升级</p>
        </div>
    </div>

    <div class="max-w-6xl mx-auto px-6 py-8">
        <!-- 格式要求说明 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">新格式要求</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-list-ol text-blue-600 text-xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-2">阿拉伯数字序号</h3>
                    <p class="text-sm text-gray-600">使用"1、2、3、4..."<br>（中文顿号）</p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-paragraph text-green-600 text-xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-2">完整段落描述</h3>
                    <p class="text-sm text-gray-600">每个条目为完整的<br>工作内容描述</p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-ban text-yellow-600 text-xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-2">无二级标题</h3>
                    <p class="text-sm text-gray-600">移除所有二级标题<br>和子分类结构</p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-briefcase text-purple-600 text-xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-2">商务正式语言</h3>
                    <p class="text-sm text-gray-600">包含具体人名、数据<br>和时间信息</p>
                </div>
            </div>
        </div>

        <!-- 格式对比 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">格式对比效果</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 优化前 -->
                <div>
                    <h3 class="text-lg font-semibold text-red-600 mb-4 flex items-center">
                        <i class="fa-solid fa-times-circle mr-2"></i> 优化前（层级结构）
                    </h3>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="space-y-3">
                            <h4 class="font-bold text-blue-600 text-base border-l-4 border-blue-600 pl-3">一、客户业务场景</h4>
                            <ul class="list-none pl-0 space-y-2">
                                <li class="flex items-start">
                                    <span class="inline-block w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-xs flex items-center justify-center mr-3 mt-0.5 flex-shrink-0 font-medium">1</span>
                                    <span class="text-gray-700">与某大型制造企业进行深度沟通</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="inline-block w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-xs flex items-center justify-center mr-3 mt-0.5 flex-shrink-0 font-medium">2</span>
                                    <span class="text-gray-700">分析客户现有IT架构</span>
                                </li>
                            </ul>
                            
                            <h4 class="font-bold text-blue-600 text-base border-l-4 border-blue-600 pl-3 mt-4">二、客户关注点</h4>
                            <ul class="list-none pl-0 space-y-2">
                                <li class="flex items-start">
                                    <span class="inline-block w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-xs flex items-center justify-center mr-3 mt-0.5 flex-shrink-0 font-medium">1</span>
                                    <span class="text-gray-700">数据安全是首要考虑</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-3 text-xs text-red-600">
                        <i class="fa-solid fa-exclamation-triangle mr-1"></i>
                        问题：层级复杂、内容分散、不符合商务报告格式
                    </div>
                </div>

                <!-- 优化后 -->
                <div>
                    <h3 class="text-lg font-semibold text-green-600 mb-4 flex items-center">
                        <i class="fa-solid fa-check-circle mr-2"></i> 优化后（商务报告格式）
                    </h3>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="space-y-4">
                            <div class="leading-relaxed">
                                <span class="font-semibold text-blue-600 mr-2">1、</span>
                                <span class="text-gray-800">对接团队情况，对接团队为研发效能团队，40-50开发人员规模，工作主要涉及内部研发提效工具平台的建设，冯上为团队负责人；</span>
                            </div>
                            
                            <div class="leading-relaxed">
                                <span class="font-semibold text-blue-600 mr-2">2、</span>
                                <span class="text-gray-800">AI Coding现状，目前滴滴有自研IDE插件，内部采纳率为20%出头，关注定制模型相较开源模型带来的提升对比情况，反馈1年内暂无人力投入训练模型&微调优化；</span>
                            </div>
                            
                            <div class="leading-relaxed">
                                <span class="font-semibold text-blue-600 mr-2">3、</span>
                                <span class="text-gray-800">友商对接情况，24年与阿里、百度沟通过，但对方反馈倾向输出标品方案，不希望做模型定制化调优，导致项目搁置；</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 text-xs text-green-600">
                        <i class="fa-solid fa-check mr-1"></i>
                        改进：阿拉伯数字序号、完整段落、商务语言风格
                    </div>
                </div>
            </div>
        </div>

        <!-- 实际示例 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">实际应用示例</h2>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4">滴滴Kwaipilot拜访交流</h3>
                
                <div class="space-y-4">
                    <div class="leading-relaxed">
                        <span class="font-semibold text-blue-600 mr-2">1、</span>
                        <span class="text-gray-800">对接团队情况，对接团队为研发效能团队，40-50开发人员规模，工作主要涉及内部研发提效工具平台的建设，冯上为团队负责人；</span>
                    </div>
                    
                    <div class="leading-relaxed">
                        <span class="font-semibold text-blue-600 mr-2">2、</span>
                        <span class="text-gray-800">AI Coding现状，目前滴滴有自研IDE插件，内部采纳率为20%出头，关注定制模型相较开源模型带来的提升对比情况，反馈1年内暂无人力投入训练模型&微调优化，会考虑采购外部厂商能力，但需沿用自有插件；</span>
                    </div>
                    
                    <div class="leading-relaxed">
                        <span class="font-semibold text-blue-600 mr-2">3、</span>
                        <span class="text-gray-800">友商对接情况，24年与阿里、百度沟通过，但对方反馈倾向输出标品方案，不希望做模型定制化调优，导致项目搁置，25年暂未做过正式交流推进；</span>
                    </div>
                    
                    <div class="leading-relaxed">
                        <span class="font-semibold text-blue-600 mr-2">4、</span>
                        <span class="text-gray-800">POC预期推进计划，代码续写+RAG，一期会覆盖50人左右，A100+L20共4张，若采纳率提升达到5%-10%，则考虑扩大POC范围至200人左右，最终采纳率提升大于10%会考虑采购。端午节后客户用2周左右时间完成接口协议+插件改造说明+GPU资源申请。</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术特性 -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <h3 class="font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fa-solid fa-cogs text-blue-600 mr-2"></i> 智能合并算法
                </h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• 自动识别分类标题</li>
                    <li>• 智能合并相关内容</li>
                    <li>• 生成完整段落描述</li>
                    <li>• 保持商务语言风格</li>
                </ul>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <h3 class="font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fa-solid fa-format-list-numbered text-green-600 mr-2"></i> 格式标准化
                </h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• 阿拉伯数字 + 中文顿号</li>
                    <li>• 统一的段落结构</li>
                    <li>• 自动添加分号结尾</li>
                    <li>• 清理多余序号标记</li>
                </ul>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <h3 class="font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fa-solid fa-file-alt text-purple-600 mr-2"></i> 商务报告风格
                </h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• 包含具体人名和数据</li>
                    <li>• 时间节点明确</li>
                    <li>• 完整的工作描述</li>
                    <li>• 专业的语言表达</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <div class="bg-gray-800 text-white py-8 mt-12">
        <div class="max-w-6xl mx-auto px-6 text-center">
            <p class="text-gray-300">工作周报AI编辑器 - 格式优化完成</p>
            <p class="text-sm text-gray-400 mt-2">从层级结构到商务报告格式的全面升级</p>
        </div>
    </div>
</body>
</html>
